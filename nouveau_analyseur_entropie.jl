"""
    NouveauAnalyseurEntropie

Programme d'analyse mathématique basé sur les 24 fonctions de Formules1.txt.
Implémente une double approche : probabilités observées vs théoriques INDEX5.

Basé sur l'architecture d'entropie_baccarat_analyzer.jl mais avec de nouvelles métriques.
Chaque formule est implémentée deux fois : version observée et version théorique.

Auteur: Assistant IA
Version: 1.0.0
Date: 2025-01-11
"""

# ═══════════════════════════════════════════════════════════════════════════════
# IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════════════════

using JSON
using Statistics
using LinearAlgebra
using Printf

# ═══════════════════════════════════════════════════════════════════════════════
# TYPES ET STRUCTURES DE DONNÉES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MonAnalyseur{T<:AbstractFloat}

Analyseur principal pour les calculs d'entropie avec double approche.
Contient les probabilités théoriques INDEX5 et les paramètres de calcul.
"""
struct MonAnalyseur{T<:AbstractFloat}
    base::T
    epsilon::T
    probabilites_theoriques::Dict{String,T}
    
    function MonAnalyseur{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        # Validation des paramètres
        if base <= 0 || base == 1
            throw(ArgumentError("Base must be > 0 and ≠ 1"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon must be > 0"))
        end
        
        # Probabilités théoriques INDEX5 (copiées d'entropie_baccarat_analyzer.jl)
        probabilites_theoriques = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.049102), "1_C_BANKER" => T(0.049695),
            "0_A_PLAYER" => T(0.081473), "1_A_PLAYER" => T(0.082693),
            "0_B_PLAYER" => T(0.061906), "1_B_PLAYER" => T(0.062721),
            "0_C_PLAYER" => T(0.047013), "1_C_PLAYER" => T(0.047588),
            "0_A_TIE" => T(0.008391), "1_A_TIE" => T(0.008518),
            "0_B_TIE" => T(0.006376), "1_B_TIE" => T(0.006462),
            "0_C_TIE" => T(0.004844), "1_C_TIE" => T(0.004906),
            "0_UNKNOWN" => T(0.000001), "1_UNKNOWN" => T(0.000001)
        )
        
        new{T}(base, epsilon, probabilites_theoriques)
    end
end

# Constructeur de convenance
MonAnalyseur(args...) = MonAnalyseur{Float64}(args...)

"""
    MesMetriques{T<:AbstractFloat}

Structure contenant les 24 métriques calculées (12 formules × 2 versions).
Chaque position dans la séquence génère une instance de cette structure.
"""
struct MesMetriques{T<:AbstractFloat}
    position::Int
    
    # Formules fondamentales (10 champs : 5 formules × 2 versions)
    formule1A_shannon_jointe_obs::T
    formule1B_shannon_jointe_theo::T
    formule2A_aep_obs::T
    formule2B_aep_theo::T
    formule3A_taux_entropie_obs::T
    formule3B_taux_entropie_theo::T
    formule4A_entropie_metrique_obs::T
    formule4B_entropie_metrique_theo::T
    formule5A_conditionnelle_obs::T
    formule5B_conditionnelle_theo::T
    
    # Formules avancées (8 champs : 4 formules × 2 versions)
    formule6A_divergence_kl_obs_theo::T
    formule6B_divergence_kl_theo_unif::T
    formule7A_information_mutuelle_obs::T
    formule7B_information_mutuelle_theo::T
    formule8A_entropie_croisee_obs_theo::T
    formule8B_entropie_croisee_theo_unif::T
    formule9A_entropie_topologique_obs::T
    formule9B_entropie_topologique_theo::T
    
    # Formules spécialisées (6 champs : 3 formules × 2 versions)
    formule10A_block_cumulative_obs::T
    formule10B_block_cumulative_theo::T
    formule11A_conditionnelle_decroissante_obs::T
    formule11B_conditionnelle_decroissante_theo::T
    formule12A_theoreme_aep_obs::T
    formule12B_theoreme_aep_theo::T
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat

Calcul sécurisé du logarithme avec protection contre log(0).
"""
function safe_log(x::T, base::T, epsilon::T) where T<:AbstractFloat
    if x <= epsilon
        return log(epsilon) / log(base)
    else
        return log(x) / log(base)
    end
end

"""
    validate_probabilities(probs::Vector{T}) where T<:AbstractFloat

Validation des probabilités (non-négatives et somme proche de 1).
"""
function validate_probabilities(probs::Vector{T}) where T<:AbstractFloat
    if any(p -> p < 0, probs)
        throw(ArgumentError("Probabilities must be non-negative"))
    end
    
    total = sum(probs)
    if abs(total - 1.0) > 1e-10
        @warn "Probabilities sum to $total, not 1.0"
    end
    
    return true
end

"""
    calculer_probabilites_observees(sequence::Vector{String})

Calcule les probabilités observées à partir des fréquences dans la séquence.
"""
function calculer_probabilites_observees(sequence::Vector{String})
    if isempty(sequence)
        return Dict{String,Float64}()
    end
    
    counts = Dict{String,Int}()
    for item in sequence
        counts[item] = get(counts, item, 0) + 1
    end
    
    n = length(sequence)
    return Dict(k => v/n for (k,v) in counts)
end

"""
    obtenir_probabilites_theoriques(analyseur::MonAnalyseur{T}, modele::String) where T

Récupère les probabilités théoriques selon le modèle spécifié.
"""
function obtenir_probabilites_theoriques(analyseur::MonAnalyseur{T}, modele::String = "INDEX5") where T
    if modele == "INDEX5"
        return analyseur.probabilites_theoriques
    elseif modele == "UNIFORME"
        # Distribution uniforme sur les 18 valeurs INDEX5
        n_values = length(analyseur.probabilites_theoriques)
        uniform_prob = T(1.0) / T(n_values)
        return Dict(k => uniform_prob for k in keys(analyseur.probabilites_theoriques))
    else
        throw(ArgumentError("Modèle non supporté: $modele"))
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CHARGEMENT ET TRAITEMENT DES DONNÉES JSON
# ═══════════════════════════════════════════════════════════════════════════════

"""
    load_baccarat_data(filepath::String)

Charge les données de baccarat depuis un fichier JSON.
Retourne un vecteur de dictionnaires représentant les parties.
"""
function load_baccarat_data(filepath::String)
    try
        # Vérification de l'existence du fichier
        if !isfile(filepath)
            throw(SystemError("File not found: $filepath"))
        end

        # Lecture et parsing du JSON
        json_content = read(filepath, String)
        if isempty(strip(json_content))
            throw(ArgumentError("File is empty: $filepath"))
        end

        data = JSON.parse(json_content)

        # Validation de la structure
        if !isa(data, Vector)
            throw(ArgumentError("JSON root must be an array"))
        end

        if isempty(data)
            @warn "No games found in file: $filepath"
            return Dict[]
        end

        # Validation de chaque partie
        for (i, game) in enumerate(data)
            if !isa(game, Dict)
                throw(ArgumentError("Game $i is not a dictionary"))
            end

            if !haskey(game, "mains")
                throw(ArgumentError("Game $i missing 'mains' key"))
            end

            if !isa(game["mains"], Vector)
                throw(ArgumentError("Game $i 'mains' is not an array"))
            end
        end

        println("✅ Successfully loaded $(length(data)) games from $filepath")
        return data

    catch e
        if isa(e, SystemError)
            println("❌ File error: $(e.msg)")
        elseif isa(e, ArgumentError) && occursin("JSON", string(e))
            println("❌ JSON parsing error: $e")
        else
            println("❌ Error loading data: $e")
        end
        return Dict[]
    end
end

"""
    extract_index5_sequence(game_data::Dict)

Extrait la séquence INDEX5 d'une partie de baccarat.
Retourne un vecteur de chaînes représentant les valeurs INDEX5.
"""
function extract_index5_sequence(game_data::Dict)
    try
        # Validation de la structure de base
        if !haskey(game_data, "mains")
            throw(ArgumentError("Game data missing 'mains' key"))
        end

        mains = game_data["mains"]
        if !isa(mains, Vector) || isempty(mains)
            throw(ArgumentError("'mains' must be a non-empty array"))
        end

        sequence = String[]

        # Extraction des valeurs INDEX5
        for (i, main) in enumerate(mains)
            if !isa(main, Dict)
                @warn "Main $i is not a dictionary, skipping"
                continue
            end

            if !haskey(main, "INDEX5")
                @warn "Main $i missing INDEX5, skipping"
                continue
            end

            index5_value = string(main["INDEX5"])

            # Validation du format INDEX5
            if !occursin(r"^[01]_[ABC]_(BANKER|PLAYER|TIE)$", index5_value)
                @warn "Main $i has invalid INDEX5 format: $index5_value, skipping"
                continue
            end

            push!(sequence, index5_value)
        end

        if isempty(sequence)
            @warn "No valid INDEX5 values found in game"
        else
            println("📊 Extracted $(length(sequence)) INDEX5 values")
        end

        return sequence

    catch e
        println("❌ Error extracting INDEX5 sequence: $e")
        return String[]
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS MATHÉMATIQUES - 24 FONCTIONS (12 FORMULES × 2 VERSIONS)
# ═══════════════════════════════════════════════════════════════════════════════

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES FONDAMENTALES (1-5) - 10 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule1A_shannon_jointe_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 1A : Entropie de Shannon jointe observée
H_obs(X₁,...,Xₙ) = -∑ p_obs(x₁,...,xₙ) log₂ p_obs(x₁,...,xₙ)
"""
function calculer_formule1A_shannon_jointe_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Calcul des probabilités observées pour la séquence complète
    # Pour l'entropie jointe, on considère la séquence entière comme un seul événement
    sequence_str = join(sequence, ",")
    p_obs = T(1.0)  # Probabilité de cette séquence spécifique = 1 (elle est observée)

    # Pour une séquence observée, l'entropie jointe est 0 (certitude)
    # Mais on peut calculer l'entropie basée sur les patterns
    probs_obs = calculer_probabilites_observees(sequence)

    entropy = T(0.0)
    for (_, prob) in probs_obs
        if prob > analyseur.epsilon
            entropy -= prob * safe_log(prob, analyseur.base, analyseur.epsilon)
        end
    end

    return entropy * length(sequence)  # Ajustement pour la longueur
end

"""
    calculer_formule1B_shannon_jointe_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 1B : Entropie de Shannon jointe théorique
H_theo(X₁,...,Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)
"""
function calculer_formule1B_shannon_jointe_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Calcul basé sur les probabilités théoriques INDEX5
    probs_theo = analyseur.probabilites_theoriques

    entropy = T(0.0)
    for symbol in sequence
        if haskey(probs_theo, symbol)
            prob = probs_theo[symbol]
            if prob > analyseur.epsilon
                entropy -= safe_log(prob, analyseur.base, analyseur.epsilon)
            end
        end
    end

    return entropy
end

"""
    calculer_formule2A_aep_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 2A : Entropie AEP observée
H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)
"""
function calculer_formule2A_aep_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    probs_obs = calculer_probabilites_observees(sequence)
    n = length(sequence)

    sum_log = T(0.0)
    for symbol in sequence
        if haskey(probs_obs, symbol)
            prob = probs_obs[symbol]
            sum_log += safe_log(prob, analyseur.base, analyseur.epsilon)
        end
    end

    return -sum_log / T(n)
end

"""
    calculer_formule2B_aep_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 2B : Entropie AEP théorique
H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)
"""
function calculer_formule2B_aep_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    probs_theo = analyseur.probabilites_theoriques
    n = length(sequence)

    sum_log = T(0.0)
    for symbol in sequence
        if haskey(probs_theo, symbol)
            prob = probs_theo[symbol]
            sum_log += safe_log(prob, analyseur.base, analyseur.epsilon)
        end
    end

    return -sum_log / T(n)
end

"""
    calculer_formule3A_taux_entropie_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 3A : Taux d'entropie observé
H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁,...,Xₙ)
"""
function calculer_formule3A_taux_entropie_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation du taux d'entropie par l'entropie jointe divisée par n
    entropy_jointe = calculer_formule1A_shannon_jointe_obs(analyseur, sequence)
    return entropy_jointe / T(length(sequence))
end

"""
    calculer_formule3B_taux_entropie_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 3B : Taux d'entropie théorique
H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁,...,Xₙ)
"""
function calculer_formule3B_taux_entropie_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation du taux d'entropie par l'entropie jointe divisée par n
    entropy_jointe = calculer_formule1B_shannon_jointe_theo(analyseur, sequence)
    return entropy_jointe / T(length(sequence))
end

"""
    calculer_formule4A_entropie_metrique_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 4A : Entropie métrique observée (Kolmogorov-Sinai)
h_μ_obs(T) = sup{h_μ_obs(T,α) : α partition finie}
"""
function calculer_formule4A_entropie_metrique_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation pratique : Mt5_obs = H_AEP_obs / log₂(18)
    h_aep_obs = calculer_formule2A_aep_obs(analyseur, sequence)
    return h_aep_obs / safe_log(T(18), analyseur.base, analyseur.epsilon)
end

"""
    calculer_formule4B_entropie_metrique_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 4B : Entropie métrique théorique (Kolmogorov-Sinai)
h_μ_theo(T) = sup{h_μ_theo(T,α) : α partition finie}
"""
function calculer_formule4B_entropie_metrique_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation pratique : Mt5_theo = H_AEP_theo / log₂(18)
    h_aep_theo = calculer_formule2B_aep_theo(analyseur, sequence)
    return h_aep_theo / safe_log(T(18), analyseur.base, analyseur.epsilon)
end

"""
    calculer_formule5A_conditionnelle_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 5A : Entropie conditionnelle observée
H_obs(Xₙ|X₁,...,Xₙ₋₁) = H_obs(X₁,...,Xₙ) - H_obs(X₁,...,Xₙ₋₁)
"""
function calculer_formule5A_conditionnelle_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if length(sequence) <= 1
        return T(0.0)
    end

    # H(X₁,...,Xₙ)
    h_n = calculer_formule1A_shannon_jointe_obs(analyseur, sequence)

    # H(X₁,...,Xₙ₋₁)
    sequence_n_minus_1 = sequence[1:end-1]
    h_n_minus_1 = calculer_formule1A_shannon_jointe_obs(analyseur, sequence_n_minus_1)

    return h_n - h_n_minus_1
end

"""
    calculer_formule5B_conditionnelle_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 5B : Entropie conditionnelle théorique
H_theo(Xₙ|X₁,...,Xₙ₋₁) = H_theo(X₁,...,Xₙ) - H_theo(X₁,...,Xₙ₋₁)
"""
function calculer_formule5B_conditionnelle_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if length(sequence) <= 1
        return T(0.0)
    end

    # H(X₁,...,Xₙ)
    h_n = calculer_formule1B_shannon_jointe_theo(analyseur, sequence)

    # H(X₁,...,Xₙ₋₁)
    sequence_n_minus_1 = sequence[1:end-1]
    h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(analyseur, sequence_n_minus_1)

    return h_n - h_n_minus_1
end

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES AVANCÉES (6-9) - 8 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule6A_divergence_kl_obs_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 6A : Divergence KL observée → théorique
D_KL_obs_theo = ∑ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))
"""
function calculer_formule6A_divergence_kl_obs_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    probs_obs = calculer_probabilites_observees(sequence)
    probs_theo = analyseur.probabilites_theoriques

    divergence = T(0.0)
    for (symbol, p_obs) in probs_obs
        if haskey(probs_theo, symbol) && p_obs > analyseur.epsilon
            p_theo = probs_theo[symbol]
            if p_theo > analyseur.epsilon
                ratio = p_obs / p_theo
                divergence += p_obs * safe_log(ratio, analyseur.base, analyseur.epsilon)
            end
        end
    end

    return divergence
end

"""
    calculer_formule6B_divergence_kl_theo_unif(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 6B : Divergence KL théorique → uniforme
D_KL_theo_unif = ∑ p_theo(xᵢ) log₂(p_theo(xᵢ)/p_unif(xᵢ))
"""
function calculer_formule6B_divergence_kl_theo_unif(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    probs_theo = analyseur.probabilites_theoriques
    probs_unif = obtenir_probabilites_theoriques(analyseur, "UNIFORME")

    divergence = T(0.0)
    for (symbol, p_theo) in probs_theo
        if p_theo > analyseur.epsilon
            p_unif = probs_unif[symbol]
            if p_unif > analyseur.epsilon
                ratio = p_theo / p_unif
                divergence += p_theo * safe_log(ratio, analyseur.base, analyseur.epsilon)
            end
        end
    end

    return divergence
end

"""
    calculer_formule7A_information_mutuelle_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 7A : Information mutuelle observée
I_obs(X₁ⁿ;Y₁ⁿ) = H_obs(X₁ⁿ) + H_obs(Y₁ⁿ) - H_obs(X₁ⁿ,Y₁ⁿ)
"""
function calculer_formule7A_information_mutuelle_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if length(sequence) <= 1
        return T(0.0)
    end

    # Pour simplifier, on considère X = première moitié, Y = seconde moitié
    mid = length(sequence) ÷ 2
    if mid == 0
        return T(0.0)
    end

    seq_x = sequence[1:mid]
    seq_y = sequence[mid+1:end]

    # H(X) et H(Y)
    h_x = calculer_formule1A_shannon_jointe_obs(analyseur, seq_x)
    h_y = calculer_formule1A_shannon_jointe_obs(analyseur, seq_y)

    # H(X,Y) - approximation par l'entropie de la séquence complète
    h_xy = calculer_formule1A_shannon_jointe_obs(analyseur, sequence)

    return h_x + h_y - h_xy
end

"""
    calculer_formule7B_information_mutuelle_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 7B : Information mutuelle théorique
I_theo(X₁ⁿ;Y₁ⁿ) = H_theo(X₁ⁿ) + H_theo(Y₁ⁿ) - H_theo(X₁ⁿ,Y₁ⁿ)
"""
function calculer_formule7B_information_mutuelle_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if length(sequence) <= 1
        return T(0.0)
    end

    # Pour simplifier, on considère X = première moitié, Y = seconde moitié
    mid = length(sequence) ÷ 2
    if mid == 0
        return T(0.0)
    end

    seq_x = sequence[1:mid]
    seq_y = sequence[mid+1:end]

    # H(X) et H(Y)
    h_x = calculer_formule1B_shannon_jointe_theo(analyseur, seq_x)
    h_y = calculer_formule1B_shannon_jointe_theo(analyseur, seq_y)

    # H(X,Y) - approximation par l'entropie de la séquence complète
    h_xy = calculer_formule1B_shannon_jointe_theo(analyseur, sequence)

    return h_x + h_y - h_xy
end

"""
    calculer_formule8A_entropie_croisee_obs_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 8A : Entropie croisée observée → théorique
H_cross_obs_theo = -∑ p_obs(xᵢ) log₂ p_theo(xᵢ)
"""
function calculer_formule8A_entropie_croisee_obs_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    probs_obs = calculer_probabilites_observees(sequence)
    probs_theo = analyseur.probabilites_theoriques

    cross_entropy = T(0.0)
    for (symbol, p_obs) in probs_obs
        if haskey(probs_theo, symbol) && p_obs > analyseur.epsilon
            p_theo = probs_theo[symbol]
            if p_theo > analyseur.epsilon
                cross_entropy -= p_obs * safe_log(p_theo, analyseur.base, analyseur.epsilon)
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule8B_entropie_croisee_theo_unif(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 8B : Entropie croisée théorique → uniforme
H_cross_theo_unif = -∑ p_theo(xᵢ) log₂ p_unif(xᵢ)
"""
function calculer_formule8B_entropie_croisee_theo_unif(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    probs_theo = analyseur.probabilites_theoriques
    probs_unif = obtenir_probabilites_theoriques(analyseur, "UNIFORME")

    cross_entropy = T(0.0)
    for (symbol, p_theo) in probs_theo
        if p_theo > analyseur.epsilon
            p_unif = probs_unif[symbol]
            if p_unif > analyseur.epsilon
                cross_entropy -= p_theo * safe_log(p_unif, analyseur.base, analyseur.epsilon)
            end
        end
    end

    return cross_entropy
end

"""
    calculer_formule9A_entropie_topologique_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 9A : Entropie topologique observée
h_top_obs(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_obs(ε)
"""
function calculer_formule9A_entropie_topologique_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation : nombre de motifs distincts observés
    unique_patterns = Set(sequence)
    s_n = length(unique_patterns)
    n = length(sequence)

    if s_n > 0
        return safe_log(T(s_n), analyseur.base, analyseur.epsilon) / T(n)
    else
        return T(0.0)
    end
end

"""
    calculer_formule9B_entropie_topologique_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 9B : Entropie topologique théorique
h_top_theo(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_theo(ε)
"""
function calculer_formule9B_entropie_topologique_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return T(0.0)
    end

    # Approximation basée sur le nombre de valeurs INDEX5 possibles
    n_possible = length(analyseur.probabilites_theoriques)
    n = length(sequence)

    return safe_log(T(n_possible), analyseur.base, analyseur.epsilon) / T(n)
end

# ─────────────────────────────────────────────────────────────────────────────
# FORMULES SPÉCIALISÉES (10-12) - 6 FONCTIONS
# ─────────────────────────────────────────────────────────────────────────────

"""
    calculer_formule10A_block_cumulative_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 10A : Entropie block cumulative observée (identique à formule 1A)
H_n_obs = H_obs(X₁,...,Xₙ) avec H_{n+1}_obs ≥ H_n_obs
"""
function calculer_formule10A_block_cumulative_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule1A_shannon_jointe_obs(analyseur, sequence)
end

"""
    calculer_formule10B_block_cumulative_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 10B : Entropie block cumulative théorique (identique à formule 1B)
H_n_theo = H_theo(X₁,...,Xₙ) avec H_{n+1}_theo ≥ H_n_theo
"""
function calculer_formule10B_block_cumulative_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule1B_shannon_jointe_theo(analyseur, sequence)
end

"""
    calculer_formule11A_conditionnelle_decroissante_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 11A : Entropie conditionnelle décroissante observée (identique à formule 5A)
u_n_obs = H_obs(Xₙ|X₁,...,Xₙ₋₁) avec u_{n+1}_obs ≤ u_n_obs
"""
function calculer_formule11A_conditionnelle_decroissante_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule5A_conditionnelle_obs(analyseur, sequence)
end

"""
    calculer_formule11B_conditionnelle_decroissante_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 11B : Entropie conditionnelle décroissante théorique (identique à formule 5B)
u_n_theo = H_theo(Xₙ|X₁,...,Xₙ₋₁) avec u_{n+1}_theo ≤ u_n_theo
"""
function calculer_formule11B_conditionnelle_decroissante_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule5B_conditionnelle_theo(analyseur, sequence)
end

"""
    calculer_formule12A_theoreme_aep_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 12A : Théorème AEP observé (identique à formule 2A)
lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ) presque sûrement
"""
function calculer_formule12A_theoreme_aep_obs(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule2A_aep_obs(analyseur, sequence)
end

"""
    calculer_formule12B_theoreme_aep_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Formule 12B : Théorème AEP théorique (identique à formule 2B)
lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ) presque sûrement
"""
function calculer_formule12B_theoreme_aep_theo(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    return calculer_formule2B_aep_theo(analyseur, sequence)
end

# ═══════════════════════════════════════════════════════════════════════════════
# ÉVOLUTION ET ANALYSE COMPLÈTE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_evolution_complete_24fonctions(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T

Calcule l'évolution des 24 métriques position par position dans la séquence.
"""
function calculer_evolution_complete_24fonctions(analyseur::MonAnalyseur{T}, sequence::Vector{String}) where T
    if isempty(sequence)
        return MesMetriques{T}[]
    end

    results = MesMetriques{T}[]

    for n in 1:length(sequence)
        subsequence = sequence[1:n]

        # Calcul des 24 fonctions pour la position n

        # Formules fondamentales (10 fonctions)
        f1A = calculer_formule1A_shannon_jointe_obs(analyseur, subsequence)
        f1B = calculer_formule1B_shannon_jointe_theo(analyseur, subsequence)
        f2A = calculer_formule2A_aep_obs(analyseur, subsequence)
        f2B = calculer_formule2B_aep_theo(analyseur, subsequence)
        f3A = calculer_formule3A_taux_entropie_obs(analyseur, subsequence)
        f3B = calculer_formule3B_taux_entropie_theo(analyseur, subsequence)
        f4A = calculer_formule4A_entropie_metrique_obs(analyseur, subsequence)
        f4B = calculer_formule4B_entropie_metrique_theo(analyseur, subsequence)
        f5A = calculer_formule5A_conditionnelle_obs(analyseur, subsequence)
        f5B = calculer_formule5B_conditionnelle_theo(analyseur, subsequence)

        # Formules avancées (8 fonctions)
        f6A = calculer_formule6A_divergence_kl_obs_theo(analyseur, subsequence)
        f6B = calculer_formule6B_divergence_kl_theo_unif(analyseur, subsequence)
        f7A = calculer_formule7A_information_mutuelle_obs(analyseur, subsequence)
        f7B = calculer_formule7B_information_mutuelle_theo(analyseur, subsequence)
        f8A = calculer_formule8A_entropie_croisee_obs_theo(analyseur, subsequence)
        f8B = calculer_formule8B_entropie_croisee_theo_unif(analyseur, subsequence)
        f9A = calculer_formule9A_entropie_topologique_obs(analyseur, subsequence)
        f9B = calculer_formule9B_entropie_topologique_theo(analyseur, subsequence)

        # Formules spécialisées (6 fonctions)
        f10A = calculer_formule10A_block_cumulative_obs(analyseur, subsequence)
        f10B = calculer_formule10B_block_cumulative_theo(analyseur, subsequence)
        f11A = calculer_formule11A_conditionnelle_decroissante_obs(analyseur, subsequence)
        f11B = calculer_formule11B_conditionnelle_decroissante_theo(analyseur, subsequence)
        f12A = calculer_formule12A_theoreme_aep_obs(analyseur, subsequence)
        f12B = calculer_formule12B_theoreme_aep_theo(analyseur, subsequence)

        # Création de la structure de métriques avec les 24 valeurs
        metrics = MesMetriques{T}(
            n,    # position
            f1A, f1B, f2A, f2B, f3A, f3B, f4A, f4B, f5A, f5B,     # Fondamentales
            f6A, f6B, f7A, f7B, f8A, f8B, f9A, f9B,               # Avancées
            f10A, f10B, f11A, f11B, f12A, f12B                    # Spécialisées
        )

        push!(results, metrics)
    end

    return results
end

"""
    analyze_single_game_24fonctions(analyseur::MonAnalyseur{T}, game_data::Dict, game_id::Union{String, Nothing} = nothing) where T

Analyse complète d'une partie avec les 24 fonctions mathématiques.
"""
function analyze_single_game_24fonctions(analyseur::MonAnalyseur{T}, game_data::Dict, game_id::Union{String, Nothing} = nothing) where T
    try
        # Extraction de la séquence INDEX5
        sequence = extract_index5_sequence(game_data)

        if isempty(sequence)
            return Dict("error" => "Aucune séquence INDEX5 valide trouvée")
        end

        # Calcul de l'évolution avec les 24 fonctions
        evolution = calculer_evolution_complete_24fonctions(analyseur, sequence)

        if isempty(evolution)
            return Dict("error" => "Impossible de calculer l'évolution des métriques")
        end

        # Métriques finales (dernière position)
        final_metrics = evolution[end]

        # Structure de sortie adaptée pour 24 fonctions
        results = Dict(
            "game_id" => game_id,
            "sequence_length" => length(sequence),
            "sequence" => sequence,
            "unique_values_count" => length(Set(sequence)),
            "evolution" => evolution,
            "final_metrics" => final_metrics,
            "analysis_summary" => Dict(
                # Formules fondamentales (10 valeurs)
                "formule1A_finale" => final_metrics.formule1A_shannon_jointe_obs,
                "formule1B_finale" => final_metrics.formule1B_shannon_jointe_theo,
                "formule2A_finale" => final_metrics.formule2A_aep_obs,
                "formule2B_finale" => final_metrics.formule2B_aep_theo,
                "formule3A_finale" => final_metrics.formule3A_taux_entropie_obs,
                "formule3B_finale" => final_metrics.formule3B_taux_entropie_theo,
                "formule4A_finale" => final_metrics.formule4A_entropie_metrique_obs,
                "formule4B_finale" => final_metrics.formule4B_entropie_metrique_theo,
                "formule5A_finale" => final_metrics.formule5A_conditionnelle_obs,
                "formule5B_finale" => final_metrics.formule5B_conditionnelle_theo,

                # Formules avancées (8 valeurs)
                "formule6A_finale" => final_metrics.formule6A_divergence_kl_obs_theo,
                "formule6B_finale" => final_metrics.formule6B_divergence_kl_theo_unif,
                "formule7A_finale" => final_metrics.formule7A_information_mutuelle_obs,
                "formule7B_finale" => final_metrics.formule7B_information_mutuelle_theo,
                "formule8A_finale" => final_metrics.formule8A_entropie_croisee_obs_theo,
                "formule8B_finale" => final_metrics.formule8B_entropie_croisee_theo_unif,
                "formule9A_finale" => final_metrics.formule9A_entropie_topologique_obs,
                "formule9B_finale" => final_metrics.formule9B_entropie_topologique_theo,

                # Formules spécialisées (6 valeurs)
                "formule10A_finale" => final_metrics.formule10A_block_cumulative_obs,
                "formule10B_finale" => final_metrics.formule10B_block_cumulative_theo,
                "formule11A_finale" => final_metrics.formule11A_conditionnelle_decroissante_obs,
                "formule11B_finale" => final_metrics.formule11B_conditionnelle_decroissante_theo,
                "formule12A_finale" => final_metrics.formule12A_theoreme_aep_obs,
                "formule12B_finale" => final_metrics.formule12B_theoreme_aep_theo
            )
        )

        return results

    catch e
        return Dict("error" => "Erreur lors de l'analyse: $e")
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# SYSTÈME D'AFFICHAGE ET RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    display_analysis_results_24fonctions(results::Dict)

Affiche les résultats d'analyse avec les 24 fonctions mathématiques.
"""
function display_analysis_results_24fonctions(results::Dict)
    # Gestion des erreurs
    if haskey(results, "error")
        println("❌ ERREUR: $(results["error"])")
        return
    end

    # En-tête formaté
    println("═" ^ 80)
    println("📊 RÉSULTATS D'ANALYSE AVEC 24 FONCTIONS MATHÉMATIQUES")
    println("═" ^ 80)

    # Informations de base
    println("🎯 INFORMATIONS DE BASE :")
    println("   • ID de la partie          : $(get(results, "game_id", "N/A"))")
    println("   • Longueur de la séquence  : $(results["sequence_length"]) mains")
    println("   • Valeurs uniques          : $(results["unique_values_count"]) types")

    println("\n" * "─" ^ 80)

    summary = results["analysis_summary"]

    println("📊 MÉTRIQUES FINALES (24 FONCTIONS - 12 FORMULES × 2 VERSIONS) :")
    println()

    # Formules fondamentales
    println("🔹 FORMULES FONDAMENTALES (1-5) :")
    @printf("   • F1A Shannon Jointe (Obs)      : %.6f unités\n", summary["formule1A_finale"])
    @printf("   • F1B Shannon Jointe (Theo)     : %.6f unités\n", summary["formule1B_finale"])
    @printf("   • F2A AEP (Obs)                 : %.6f unités\n", summary["formule2A_finale"])
    @printf("   • F2B AEP (Theo)                : %.6f unités\n", summary["formule2B_finale"])
    @printf("   • F3A Taux Entropie (Obs)       : %.6f unités\n", summary["formule3A_finale"])
    @printf("   • F3B Taux Entropie (Theo)      : %.6f unités\n", summary["formule3B_finale"])
    @printf("   • F4A Entropie Métrique (Obs)   : %.6f unités\n", summary["formule4A_finale"])
    @printf("   • F4B Entropie Métrique (Theo)  : %.6f unités\n", summary["formule4B_finale"])
    @printf("   • F5A Conditionnelle (Obs)      : %.6f unités\n", summary["formule5A_finale"])
    @printf("   • F5B Conditionnelle (Theo)     : %.6f unités\n", summary["formule5B_finale"])

    println()

    # Formules avancées
    println("🔹 FORMULES AVANCÉES (6-9) :")
    @printf("   • F6A Divergence KL (Obs→Theo)  : %.6f unités\n", summary["formule6A_finale"])
    @printf("   • F6B Divergence KL (Theo→Unif) : %.6f unités\n", summary["formule6B_finale"])
    @printf("   • F7A Info Mutuelle (Obs)       : %.6f unités\n", summary["formule7A_finale"])
    @printf("   • F7B Info Mutuelle (Theo)      : %.6f unités\n", summary["formule7B_finale"])
    @printf("   • F8A Entropie Croisée (O→T)    : %.6f unités\n", summary["formule8A_finale"])
    @printf("   • F8B Entropie Croisée (T→U)    : %.6f unités\n", summary["formule8B_finale"])
    @printf("   • F9A Entropie Topologique (Obs): %.6f unités\n", summary["formule9A_finale"])
    @printf("   • F9B Entropie Topologique (Theo): %.6f unités\n", summary["formule9B_finale"])

    println()

    # Formules spécialisées
    println("🔹 FORMULES SPÉCIALISÉES (10-12) :")
    @printf("   • F10A Block Cumulative (Obs)   : %.6f unités\n", summary["formule10A_finale"])
    @printf("   • F10B Block Cumulative (Theo)  : %.6f unités\n", summary["formule10B_finale"])
    @printf("   • F11A Cond. Décroissante (Obs) : %.6f unités\n", summary["formule11A_finale"])
    @printf("   • F11B Cond. Décroissante (Theo): %.6f unités\n", summary["formule11B_finale"])
    @printf("   • F12A Théorème AEP (Obs)       : %.6f unités\n", summary["formule12A_finale"])
    @printf("   • F12B Théorème AEP (Theo)      : %.6f unités\n", summary["formule12B_finale"])

    println("\n" * "═" ^ 80)
end

"""
    generate_metrics_table_report_24fonctions(results::Dict) -> String

Génère un rapport texte détaillé pour les 24 fonctions.
"""
function generate_metrics_table_report_24fonctions(results::Dict)
    if haskey(results, "error")
        return "ERREUR: $(results["error"])"
    end

    report = IOBuffer()

    println(report, "═" ^ 120)
    println(report, "📊 RAPPORT DÉTAILLÉ - 24 FONCTIONS MATHÉMATIQUES")
    println(report, "═" ^ 120)

    println(report, "🎯 INFORMATIONS DE BASE :")
    println(report, "   • ID de la partie          : $(get(results, "game_id", "N/A"))")
    println(report, "   • Longueur de la séquence  : $(results["sequence_length"]) mains")
    println(report, "   • Valeurs uniques          : $(results["unique_values_count"]) types")

    println(report, "\n📊 MÉTRIQUES FINALES :")
    summary = results["analysis_summary"]

    # Affichage des 24 métriques finales
    for (key, value) in sort(collect(summary))
        println(report, @sprintf("   • %-30s : %.6f unités", key, value))
    end

    println(report, "\n" * "═" ^ 120)

    return String(take!(report))
end

"""
    display_theoretical_probabilities_24fonctions(analyseur::MonAnalyseur)

Affiche les probabilités théoriques INDEX5 utilisées dans les calculs.
"""
function display_theoretical_probabilities_24fonctions(analyseur::MonAnalyseur)
    println("═" ^ 80)
    println("📊 PROBABILITÉS THÉORIQUES INDEX5")
    println("═" ^ 80)

    # Tri par probabilité décroissante
    sorted_probs = sort(collect(analyseur.probabilites_theoriques), by = x -> x[2], rev = true)

    println(@sprintf("%-15s %12s %8s", "INDEX5", "Probabilité", "Pourcentage"))
    println("─" ^ 40)

    for (index5, prob) in sorted_probs
        println(@sprintf("%-15s %12.6f %8.4f%%", index5, prob, prob * 100))
    end

    # Statistiques globales
    probs = collect(values(analyseur.probabilites_theoriques))
    total = sum(probs)

    println("\n🔹 STATISTIQUES GLOBALES :")
    @printf("   • Total           : %.6f\n", total)
    @printf("   • Moyenne         : %.6f\n", mean(probs))
    @printf("   • Écart-type      : %.6f\n", std(probs))
    @printf("   • Min/Max         : %.6f / %.6f\n", minimum(probs), maximum(probs))
    @printf("   • Entropie max    : %.6f bits (distribution uniforme)\n", log2(length(probs)))

    # Calcul d'entropie théorique
    theoretical_entropy = -sum(p * log2(p) for p in probs if p > 0)
    @printf("   • Entropie réelle : %.6f bits\n", theoretical_entropy)

    println("═" ^ 80)
end

# ═══════════════════════════════════════════════════════════════════════════════
# INTERFACE UTILISATEUR INTERACTIVE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale avec menu interactif pour l'analyse des 24 fonctions.
"""
function main()
    println("═" ^ 80)
    println("🧮 ANALYSEUR D'ENTROPIE - 24 FONCTIONS MATHÉMATIQUES")
    println("═" ^ 80)
    println("📚 Basé sur les formules de Formules1.txt")
    println("🔄 Double approche : Probabilités observées vs théoriques INDEX5")
    println("═" ^ 80)

    # Initialisation de l'analyseur
    analyseur = MonAnalyseur{Float64}()

    # Chemin par défaut vers le fichier de données
    default_filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"

    while true
        println("\n🎯 MENU PRINCIPAL:")
        println("1. Analyser une partie unique")
        println("2. Analyser plusieurs parties")
        println("3. Afficher les probabilités théoriques INDEX5")
        println("4. Quitter")

        print("\n👉 Votre choix (1-4) : ")
        choix = strip(readline())

        if choix == "1"
            analyze_single_game_interactive_24fonctions(analyseur, default_filepath)
        elseif choix == "2"
            analyze_multiple_games_interactive_24fonctions(analyseur, default_filepath)
        elseif choix == "3"
            display_theoretical_probabilities_24fonctions(analyseur)
        elseif choix == "4"
            println("👋 Au revoir! Merci d'avoir utilisé l'analyseur!")
            break
        else
            println("❌ Choix invalide. Veuillez sélectionner 1, 2, 3 ou 4.")
        end
    end
end

"""
    analyze_single_game_interactive_24fonctions(analyseur::MonAnalyseur, filepath::String)

Interface interactive pour analyser une partie unique avec les 24 fonctions.
"""
function analyze_single_game_interactive_24fonctions(analyseur::MonAnalyseur, filepath::String)
    println("\n" * "─" ^ 60)
    println("📊 ANALYSE D'UNE PARTIE UNIQUE")
    println("─" ^ 60)

    # Chargement des données
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        println("❌ Aucune partie trouvée dans le fichier.")
        return
    end

    println("📈 $(length(parties)) parties disponibles.")
    print("👉 Numéro de la partie à analyser (1-$(length(parties))) : ")

    try
        partie_num = parse(Int, readline())

        if partie_num < 1 || partie_num > length(parties)
            println("❌ Numéro de partie invalide!")
            return
        end

        # Analyse de la partie sélectionnée
        game_data = parties[partie_num]
        game_id = "Partie_$partie_num"

        println("\n🔄 Analyse en cours...")
        results = analyze_single_game_24fonctions(analyseur, game_data, game_id)

        # Affichage des résultats
        display_analysis_results_24fonctions(results)

        # Génération automatique du rapport
        if !haskey(results, "error")
            rapport_filename = "partie$(partie_num)_24fonctions.txt"
            rapport_content = generate_metrics_table_report_24fonctions(results)

            try
                open(rapport_filename, "w") do file
                    write(file, rapport_content)
                end
                println("\n📄 Rapport automatiquement généré : $rapport_filename")
            catch e
                println("\n❌ Erreur lors de la génération du rapport : $e")
            end
        end

    catch e
        println("❌ Erreur: $e")
    end
end

"""
    analyze_multiple_games_interactive_24fonctions(analyseur::MonAnalyseur, filepath::String)

Interface interactive pour analyser plusieurs parties avec statistiques globales.
"""
function analyze_multiple_games_interactive_24fonctions(analyseur::MonAnalyseur, filepath::String)
    println("\n" * "─" ^ 60)
    println("📊 ANALYSE DE PLUSIEURS PARTIES")
    println("─" ^ 60)

    # Chargement des données
    parties = load_baccarat_data(filepath)

    if isempty(parties)
        println("❌ Aucune partie trouvée dans le fichier.")
        return
    end

    println("📈 $(length(parties)) parties disponibles.")
    print("👉 Nombre de parties à analyser (1-$(length(parties))) : ")

    try
        num_parties = parse(Int, readline())

        if num_parties < 1 || num_parties > length(parties)
            println("❌ Nombre de parties invalide!")
            return
        end

        # Analyse multiple
        println("\n🔄 Analyse de $num_parties parties en cours...")

        all_metrics = Dict{String, Vector{Float64}}()
        all_lengths = Int[]
        successful_analyses = 0

        # Initialisation des vecteurs pour chaque métrique
        for i in 1:24
            all_metrics["formule$(i)_finale"] = Float64[]
        end

        for i in 1:num_parties
            game_data = parties[i]
            results = analyze_single_game_24fonctions(analyseur, game_data, "Partie_$i")

            if !haskey(results, "error")
                # Accumulation des métriques
                summary = results["analysis_summary"]
                for (key, value) in summary
                    if haskey(all_metrics, key)
                        push!(all_metrics[key], value)
                    end
                end

                push!(all_lengths, results["sequence_length"])
                successful_analyses += 1

                # Affichage de progression
                if i % 10 == 0
                    println("   ✅ $i/$num_parties parties analysées...")
                end
            else
                println("   ❌ Erreur partie $i: $(results["error"])")
            end
        end

        # Affichage des statistiques globales
        println("\n" * "═" ^ 80)
        println("📊 STATISTIQUES GLOBALES ($successful_analyses/$num_parties parties réussies)")
        println("═" ^ 80)

        @printf("📏 Longueur moyenne        : %.2f ± %.2f mains\n", mean(all_lengths), std(all_lengths))
        @printf("📏 Longueur min/max        : %d / %d mains\n", minimum(all_lengths), maximum(all_lengths))

        println("\n🔹 MOYENNES DES 24 MÉTRIQUES :")
        for (key, values) in sort(collect(all_metrics))
            if !isempty(values)
                @printf("   • %-30s : %.6f ± %.6f\n", key, mean(values), std(values))
            end
        end

        println("═" ^ 80)

    catch e
        println("❌ Erreur: $e")
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE AUTOMATIQUE
# ═══════════════════════════════════════════════════════════════════════════════

# Exécution automatique si le fichier est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
