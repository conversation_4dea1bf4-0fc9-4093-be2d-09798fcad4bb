FORMULES FONDAMENTALES POUR FENÊTRES CROISSANTES
1. ENTROPIE DE SHANNON JOINTE
Formule : H(X₁, X₂, ..., Xₙ) = -∑ p(x₁,...,xₙ) log₂ p(x₁,...,xₙ)

Ce qu'elle calcule :
Quantité d'information totale contenue dans la séquence complète de 1 à n
Mesure l'incertitude globale de la séquence entière
Application baccarat : Entropie de toute la séquence de mains jouées

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/ressources/implementations_python.py) :
1. Construire la matrice des probabilités jointes p(x₁,...,xₙ) pour toutes les combinaisons possibles
2. Aplatir la matrice en vecteur unidimensionnel : joint_p.flatten()
3. Valider et normaliser les probabilités : p = self._validate_probabilities(p)
4. Calculer log₂(p) avec protection contre log(0) : log_p = self._safe_log(p)
5. Calculer les termes d'entropie : entropy_terms = p * log_p
6. G<PERSON>rer le cas 0*log(0) = 0 : entropy_terms = np.where(p == 0, 0, entropy_terms)
7. Sommer avec signe négatif : H = -np.sum(entropy_terms)
2. ENTROPIE PAR SYMBOLE (AEP - Asymptotic Equipartition Property)
Formule : H_AEP(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_théo(xᵢ)

Ce qu'elle calcule :
Entropie moyenne par symbole dans la séquence croissante
Converge vers l'entropie par symbole du processus
Application baccarat : Entropie moyenne par main dans la séquence INDEX5

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Théorème 12) :
1. Pour chaque symbole xᵢ dans la séquence X₁ⁿ = [x₁, x₂, ..., xₙ]
2. Récupérer la probabilité théorique p_théo(xᵢ) du symbole xᵢ
3. Calculer log₂(p_théo(xᵢ)) avec protection contre log(0)
4. Sommer tous les logarithmes : ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
5. Appliquer la formule AEP : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
6. Propriété : lim_{n→∞} H_AEP(X₁ⁿ) = H(Ξ) presque sûrement (Théorème Shannon-McMillan-Breiman)
3. TAUX D'ENTROPIE (ENTROPY RATE)
Formule : H(Ξ) = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)

Ce qu'elle calcule :
Limite asymptotique de l'entropie par symbole
Mesure la complexité intrinsèque du processus
Application baccarat : Complexité fondamentale du système de jeu

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Définition 60 et Théorème 10) :
MÉTHODE 1 - Via entropies jointes :
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H(X₁, ..., Xₙ) (entropie jointe, voir Formule 1)
3. Calculer (1/n) × H(X₁, ..., Xₙ)
4. Tracer la courbe de (1/n) × H(X₁, ..., Xₙ) en fonction de n
5. Estimer la limite : H(Ξ) = lim_{n→∞} (1/n) H(X₁, ..., Xₙ)

MÉTHODE 2 - Via entropies conditionnelles (équivalente pour processus stationnaires) :
1. Calculer uₙ = H(Xₙ | X₁, ..., Xₙ₋₁) pour n = 1, 2, 3, ...
2. Propriété : la suite uₙ est décroissante et converge
3. H(Ξ) = lim_{n→∞} H(Xₙ | Xₙ₋₁, ..., X₁)
4. ENTROPIE MÉTRIQUE (KOLMOGOROV-SINAI)
Formule : h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

Ce qu'elle calcule :
Entropie dynamique du système
Taux de création d'information par le système dynamique
Application baccarat : Entropie métrique normalisée Mt5 = H_AEP / log₂(18)

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_expert/01_entropie_metrique.md) :
1. Choisir une partition mesurable α = {A₁, A₂, ..., Aₖ} de l'espace de phases
2. Pour chaque n, construire la partition jointe : ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα
   - T⁻ⁱα = {T⁻ⁱA₁, T⁻ⁱA₂, ..., T⁻ⁱAₖ} (partition transformée)
   - ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα = {⋂ᵢ₌₀ⁿ⁻¹ T⁻ⁱAⱼᵢ : j₀,...,jₙ₋₁ ∈ {1,...,k}}
3. Calculer H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) = -∑ᵢ μ(Bᵢ) log μ(Bᵢ) où Bᵢ sont les éléments de la partition jointe
4. Calculer (1/n) × H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
5. Prendre la limite : h_μ(T, α) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
6. Prendre le supremum sur toutes les partitions : h_μ(T) = sup{h_μ(T, α) : α partition finie}

IMPLÉMENTATION PRATIQUE (cours_entropie/ressources/implementations_python.py) :
- Utiliser metric_entropy_estimate() pour estimer via blocs de longueur croissante
- Calculer l'entropie par symbole pour chaque longueur de bloc
- Extrapoler la limite asymptotique
5. ENTROPIE CONDITIONNELLE CUMULATIVE
Formule : H(Xₙ|X₁, X₂, ..., Xₙ₋₁) = H(X₁, ..., Xₙ) - H(X₁, ..., Xₙ₋₁)

Ce qu'elle calcule :
Information apportée par le n-ème symbole connaissant tous les précédents
Mesure la prédictibilité du système
Application baccarat : Prédictibilité de la main n connaissant l'historique

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_debutant/03_entropie_conditionnelle.md et implementations_python.py) :
MÉTHODE 1 - Via différence d'entropies jointes :
1. Calculer H(X₁, ..., Xₙ) (entropie jointe de n variables, voir Formule 1)
2. Calculer H(X₁, ..., Xₙ₋₁) (entropie jointe de n-1 variables)
3. Appliquer la formule : H(Xₙ|X₁, ..., Xₙ₋₁) = H(X₁, ..., Xₙ) - H(X₁, ..., Xₙ₋₁)

MÉTHODE 2 - Via distribution jointe (pour cas bivariés) :
1. Construire la matrice de probabilités jointes joint_p
2. Calculer les probabilités marginales : p_x = np.sum(joint_p, axis=1)
3. Calculer H(X,Y) = shannon_entropy(joint_p.flatten())
4. Calculer H(X) = shannon_entropy(p_x)
5. H(Y|X) = H(X,Y) - H(X)

PROPRIÉTÉ IMPORTANTE : Pour processus stationnaires, la suite uₙ = H(Xₙ|X₁, ..., Xₙ₋₁) est décroissante
🔬 FORMULES AVANCÉES POUR FENÊTRES CROISSANTES
6. ENTROPIE RELATIVE (DIVERGENCE KL) CUMULATIVE
Formule : D_KL(P_n||Q_n) = ∑ᵢ₌₁ⁿ p(xᵢ) log₂(p(xᵢ)/q(xᵢ))

Ce qu'elle calcule :
Distance entre distribution observée et théorique sur la fenêtre croissante
Mesure l'écart par rapport au modèle théorique
Application baccarat : Écart entre fréquences observées et probabilités INDEX5

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_intermediaire/01_entropie_relative.md et implementations_python.py) :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p(xᵢ) = fréquence observée du symbole xᵢ dans la séquence
3. Calculer q(xᵢ) = probabilité théorique du symbole xᵢ
4. Vérifier que q(xᵢ) > 0 pour tout i (sinon D_KL = +∞)
5. Calculer le terme : p(xᵢ) × log₂(p(xᵢ)/q(xᵢ))
6. Sommer tous les termes : D_KL = ∑ᵢ₌₁ⁿ p(xᵢ) log₂(p(xᵢ)/q(xᵢ))

PROTECTION NUMÉRIQUE (implémentation Python) :
- Ajouter epsilon = 1e-12 aux probabilités nulles
- p = np.array(p) + epsilon ; q = np.array(q) + epsilon
- return np.sum(p * np.log2(p / q))

PROPRIÉTÉS : D_KL(P||Q) ≥ 0 avec égalité ssi P = Q ; Non-symétrique : D_KL(P||Q) ≠ D_KL(Q||P)
7. INFORMATION MUTUELLE CUMULATIVE
Formule : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) + H(Y₁ⁿ) - H(X₁ⁿ, Y₁ⁿ)

Ce qu'elle calcule :
Dépendance entre deux séquences croissantes
Information partagée entre les séquences
Application baccarat : Corrélation entre différents patterns de jeu

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_debutant/03_entropie_conditionnelle.md et implementations_python.py) :
1. Construire la matrice de probabilités jointes joint_p pour les deux séquences X₁ⁿ et Y₁ⁿ
2. Calculer les probabilités marginales :
   - p_x = np.sum(joint_p, axis=1)  # Marginalisation sur Y
   - p_y = np.sum(joint_p, axis=0)  # Marginalisation sur X
3. Calculer les entropies :
   - H(X₁ⁿ) = shannon_entropy(p_x)
   - H(Y₁ⁿ) = shannon_entropy(p_y)
   - H(X₁ⁿ, Y₁ⁿ) = shannon_entropy(joint_p.flatten())
4. Appliquer la formule : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) + H(Y₁ⁿ) - H(X₁ⁿ, Y₁ⁿ)

FORMULE ALTERNATIVE : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) - H(X₁ⁿ|Y₁ⁿ) = H(Y₁ⁿ) - H(Y₁ⁿ|X₁ⁿ)
PROPRIÉTÉS : I(X;Y) = I(Y;X) (symétrique) ; I(X;Y) ≥ 0 ; I(X;Y) = 0 ssi X et Y indépendants
8. ENTROPIE CROISÉE CUMULATIVE
Formule : H_cross(P_n, Q_n) = -∑ᵢ₌₁ⁿ p(xᵢ) log₂ q(xᵢ)

Ce qu'elle calcule :
Coût d'encodage avec une distribution incorrecte
Mesure l'efficacité de prédiction
Application baccarat : Efficacité du modèle INDEX5 pour prédire

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_intermediaire/01_entropie_relative.md) :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p(xᵢ) = fréquence observée du symbole xᵢ
3. Calculer q(xᵢ) = probabilité théorique/prédite du symbole xᵢ
4. Calculer le terme : p(xᵢ) × log₂(q(xᵢ))
5. Sommer tous les termes avec signe négatif : H_cross = -∑ᵢ₌₁ⁿ p(xᵢ) log₂ q(xᵢ)

RELATION AVEC DIVERGENCE KL : D_KL(P||Q) = H_cross(P,Q) - H(P)
- H_cross(P,Q) = -∑ p(x) log₂ q(x) (entropie croisée)
- H(P) = -∑ p(x) log₂ p(x) (entropie de Shannon)
- Donc : H_cross(P,Q) = H(P) + D_KL(P||Q)

INTERPRÉTATION : L'entropie croisée est l'excès de bits nécessaires pour encoder P avec le code optimal pour Q
9. ENTROPIE TOPOLOGIQUE CUMULATIVE
Formule : h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε)

Ce qu'elle calcule :
Complexité topologique du système dynamique
Croissance exponentielle des orbites distinctes
Application baccarat : Complexité géométrique des trajectoires de jeu

MÉTHODE DE CALCUL (vérifiée dans cours_entropie/niveau_expert/02_entropie_topologique.md) :
DÉFINITION VIA ENSEMBLES (n,ε)-SÉPARÉS :
1. Pour chaque ε > 0 et n ≥ 1
2. Définir la distance dynamique : d_n(x,y) = max_{0≤i≤n-1} d(f^i(x), f^i(y))
3. Trouver le plus grand ensemble E ⊂ X tel que pour x,y ∈ E distincts : d_n(x,y) ≥ ε
4. Calculer s_n(ε) = |E| (cardinalité de l'ensemble maximal (n,ε)-séparé)
5. Calculer (1/n) log s_n(ε)
6. Prendre la limite : h_top(f,ε) = lim_{n→∞} (1/n) log s_n(ε)
7. Prendre la limite : h_top(f) = lim_{ε→0} h_top(f,ε)

MÉTHODE ALTERNATIVE VIA ENSEMBLES (n,ε)-SPANNING :
- r_n(ε) = min{|E| : E est (n,ε)-spanning}
- h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log r_n(ε)

IMPLÉMENTATION PRATIQUE (cours_entropie/ressources/implementations_python.py) :
- Utiliser topological_entropy_estimate() avec approximation par blocs distincts
📐 FORMULES SPÉCIALISÉES POUR L'ANALYSE TEMPORELLE
10. ENTROPIE DE BLOCK CUMULATIVE
Formule : H_n = H(X₁, X₂, ..., Xₙ) avec H_{n+1} ≥ H_n

Ce qu'elle calcule :
Séquence croissante d'entropies de blocs
Évolution de l'information avec la longueur
Application baccarat : Croissance de l'information avec le nombre de mains

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 1 (ENTROPIE DE SHANNON JOINTE)
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H_n = H(X₁, X₂, ..., Xₙ) selon la méthode de la Formule 1
3. Vérifier la propriété de croissance : H_{n+1} ≥ H_n
4. Tracer la courbe H_n en fonction de n pour visualiser la croissance
5. Analyser la vitesse de croissance : ΔH_n = H_{n+1} - H_n

PROPRIÉTÉ FONDAMENTALE (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md) :
- H(X₁, ..., X_{n+1}) ≥ H(X₁, ..., Xₙ) toujours vraie
- L'ajout d'une variable ne peut que maintenir ou augmenter l'entropie jointe
11. ENTROPIE CONDITIONNELLE DÉCROISSANTE
Formule : u_n = H(Xₙ|X₁, ..., Xₙ₋₁) avec u_{n+1} ≤ u_n

Ce qu'elle calcule :
Séquence décroissante d'entropies conditionnelles
Amélioration de la prédictibilité avec l'historique
Application baccarat : Amélioration de la prédiction avec plus d'historique

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 5 (ENTROPIE CONDITIONNELLE CUMULATIVE)
1. Pour chaque position n = 2, 3, 4, ..., N
2. Calculer u_n = H(Xₙ|X₁, ..., Xₙ₋₁) selon la méthode de la Formule 5
3. Vérifier la propriété de décroissance : u_{n+1} ≤ u_n
4. Tracer la courbe u_n en fonction de n pour visualiser la décroissance
5. Analyser la vitesse de décroissance : Δu_n = u_{n+1} - u_n ≤ 0

PROPRIÉTÉ FONDAMENTALE (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Théorème 10) :
- Pour processus stationnaires : H(X_{n+1}|X_n, ..., X_1) ≤ H(X_n|X_{n-1}, ..., X_1)
- "Conditionner diminue l'entropie" : plus d'historique améliore la prédictibilité
- La suite u_n converge vers H(Ξ) (taux d'entropie du processus)
12. THÉORÈME AEP (SHANNON-MCMILLAN-BREIMAN)
Formule : lim_{n→∞} -(1/n) log₂ p(X₁ⁿ) = H(Ξ) presque sûrement

Ce qu'elle calcule :
Convergence de l'information par symbole vers l'entropie du processus
Propriété d'équipartition asymptotique
Application baccarat : Convergence vers l'entropie théorique INDEX5

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 2 (ENTROPIE PAR SYMBOLE AEP)
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer p(X₁ⁿ) = probabilité de la séquence observée X₁ⁿ = [x₁, x₂, ..., xₙ]
3. Calculer -(1/n) log₂ p(X₁ⁿ) = -(1/n) log₂(∏ᵢ₌₁ⁿ p(xᵢ)) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p(xᵢ)
4. Tracer la courbe de -(1/n) log₂ p(X₁ⁿ) en fonction de n
5. Vérifier la convergence vers H(Ξ) quand n → ∞

THÉORÈME FONDAMENTAL (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Théorème 12) :
- Pour processus stationnaires : convergence presque sûre vers H(Ξ)
- Base théorique de la compression de données et du codage de source
- Justifie l'utilisation de l'entropie comme mesure de complexité informationnelle

LIEN AVEC FORMULE 2 : Cette formule EST la Formule 2 dans sa forme limite asymptotique