FORMULES FONDAMENTALES POUR FENÊTRES CROISSANTES
1. ENTROPIE DE SHANNON JOINTE
Formule : H(X₁, X₂, ..., Xₙ) = -∑ p(x₁,...,xₙ) log₂ p(x₁,...,xₙ)

Ce qu'elle calcule :

Quantité d'information totale contenue dans la séquence complète de 1 à n
Mesure l'incertitude globale de la séquence entière
Application baccarat : Entropie de toute la séquence de mains jouées
2. ENTROPIE PAR SYMBOLE (AEP - Asymptotic Equipartition Property)
Formule : H_AEP(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_théo(xᵢ)

Ce qu'elle calcule :

Entropie moyenne par symbole dans la séquence croissante
Converge vers l'entropie par symbole du processus
Application baccarat : Entropie moyenne par main dans la séquence INDEX5
3. TAUX D'ENTROPIE (ENTROPY RATE)
Formule : H(Ξ) = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)

Ce qu'elle calcule :

Limite asymptotique de l'entropie par symbole
Mesure la complexité intrinsèque du processus
Application baccarat : Complexité fondamentale du système de jeu
4. ENTROPIE MÉTRIQUE (KOLMOGOROV-SINAI)
Formule : h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

Ce qu'elle calcule :

Entropie dynamique du système
Taux de création d'information par le système dynamique
Application baccarat : Entropie métrique normalisée Mt5 = H_AEP / log₂(18)
5. ENTROPIE CONDITIONNELLE CUMULATIVE
Formule : H(Xₙ|X₁, X₂, ..., Xₙ₋₁) = H(X₁, ..., Xₙ) - H(X₁, ..., Xₙ₋₁)

Ce qu'elle calcule :

Information apportée par le n-ème symbole connaissant tous les précédents
Mesure la prédictibilité du système
Application baccarat : Prédictibilité de la main n connaissant l'historique
🔬 FORMULES AVANCÉES POUR FENÊTRES CROISSANTES
6. ENTROPIE RELATIVE (DIVERGENCE KL) CUMULATIVE
Formule : D_KL(P_n||Q_n) = ∑ᵢ₌₁ⁿ p(xᵢ) log₂(p(xᵢ)/q(xᵢ))

Ce qu'elle calcule :

Distance entre distribution observée et théorique sur la fenêtre croissante
Mesure l'écart par rapport au modèle théorique
Application baccarat : Écart entre fréquences observées et probabilités INDEX5
7. INFORMATION MUTUELLE CUMULATIVE
Formule : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) + H(Y₁ⁿ) - H(X₁ⁿ, Y₁ⁿ)

Ce qu'elle calcule :

Dépendance entre deux séquences croissantes
Information partagée entre les séquences
Application baccarat : Corrélation entre différents patterns de jeu
8. ENTROPIE CROISÉE CUMULATIVE
Formule : H_cross(P_n, Q_n) = -∑ᵢ₌₁ⁿ p(xᵢ) log₂ q(xᵢ)

Ce qu'elle calcule :

Coût d'encodage avec une distribution incorrecte
Mesure l'efficacité de prédiction
Application baccarat : Efficacité du modèle INDEX5 pour prédire
9. ENTROPIE TOPOLOGIQUE CUMULATIVE
Formule : h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε)

Ce qu'elle calcule :

Complexité topologique du système dynamique
Croissance exponentielle des orbites distinctes
Application baccarat : Complexité géométrique des trajectoires de jeu
📐 FORMULES SPÉCIALISÉES POUR L'ANALYSE TEMPORELLE
10. ENTROPIE DE BLOCK CUMULATIVE
Formule : H_n = H(X₁, X₂, ..., Xₙ) avec H_{n+1} ≥ H_n

Ce qu'elle calcule :

Séquence croissante d'entropies de blocs
Évolution de l'information avec la longueur
Application baccarat : Croissance de l'information avec le nombre de mains
11. ENTROPIE CONDITIONNELLE DÉCROISSANTE
Formule : u_n = H(Xₙ|X₁, ..., Xₙ₋₁) avec u_{n+1} ≤ u_n

Ce qu'elle calcule :

Séquence décroissante d'entropies conditionnelles
Amélioration de la prédictibilité avec l'historique
Application baccarat : Amélioration de la prédiction avec plus d'historique
12. THÉORÈME AEP (SHANNON-MCMILLAN-BREIMAN)
Formule : lim_{n→∞} -(1/n) log₂ p(X₁ⁿ) = H(Ξ) presque sûrement

Ce qu'elle calcule :

Convergence de l'information par symbole vers l'entropie du processus
Propriété d'équipartition asymptotique
Application baccarat : Convergence vers l'entropie théorique INDEX5