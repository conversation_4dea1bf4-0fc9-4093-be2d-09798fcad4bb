FORMULES FONDAMENTALES POUR FENÊTRES CROISSANTES

1. ENTROPIE DE SHANNON JOINTE
Formule : H(X₁, X₂, ..., Xₙ) = -∑ p(x₁,...,xₙ) log₂ p(x₁,...,xₙ)

Ce qu'elle calcule :
Quantité d'information totale contenue dans la séquence complète de 1 à n
Mesure l'incertitude globale de la séquence entière
Application baccarat : Entropie de toute la séquence de mains jouées

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 912, 1160, 1723-1725)
- Ligne 912 : "l'entropie jointe H(X₁, ..., Xₙ)"
- Ligne 1160 : "l'entropie jointe H(X₁ⁿ)"
- Lignes 1723-1725 : "l'entropie jointe H(X_{α,1}, ..., X_{α,n})"

MÉTHODE DE CALCUL (basée sur cours_entropie/niveau_debutant/02_formule_shannon.md, lignes 19-20) :
1. Identifier toutes les combinaisons possibles (x₁,...,xₙ) dans la séquence
2. Calculer la probabilité jointe p(x₁,...,xₙ) pour chaque combinaison
3. Vérifier que ∑ p(x₁,...,xₙ) = 1 (normalisation)
4. Pour chaque combinaison, calculer le terme : -p(x₁,...,xₙ) × log₂(p(x₁,...,xₙ))
5. Gérer le cas p = 0 : par convention 0 × log₂(0) = 0
6. Sommer tous les termes : H(X₁,...,Xₙ) = -∑ p(x₁,...,xₙ) log₂(p(x₁,...,xₙ))
7. Le résultat est exprimé en bits d'information

2. ENTROPIE PAR SYMBOLE (AEP - Asymptotic Equipartition Property)
Formule : H_AEP(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_théo(xᵢ)

Ce qu'elle calcule :
Entropie moyenne par symbole dans la séquence croissante
Converge vers l'entropie par symbole du processus
Application baccarat : Entropie moyenne par main dans la séquence INDEX5

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 1151-1154)
- Théorème 12 (Shannon, McMillan, Breiman)
- Formule : lim_{n→∞} -(1/n) log₂ p₁ⁿ(X₁ⁿ) = H(Ξ) presque sûrement

MÉTHODE DE CALCUL (basée sur le Théorème 12) :
1. Pour chaque symbole xᵢ dans la séquence X₁ⁿ = [x₁, x₂, ..., xₙ]
2. Récupérer la probabilité théorique p_théo(xᵢ) du symbole xᵢ
3. Calculer log₂(p_théo(xᵢ)) avec protection contre log(0)
4. Sommer tous les logarithmes : ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
5. Appliquer la formule AEP : H_AEP = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
6. Propriété : lim_{n→∞} H_AEP(X₁ⁿ) = H(Ξ) presque sûrement (Théorème Shannon-McMillan-Breiman)

3. TAUX D'ENTROPIE (ENTROPY RATE)
Formule : H(Ξ) = lim_{n→∞} (1/n) H(X₁, X₂, ..., Xₙ)

Ce qu'elle calcule :
Limite asymptotique de l'entropie par symbole
Mesure la complexité intrinsèque du processus
Application baccarat : Complexité fondamentale du système de jeu

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 916-919 et 923-925)
- Définition 60 : H(Ξ) = lim_{n→∞} (1/n) H(X₁, ..., Xₙ)
- Théorème 10 : H(Ξ) = lim_{n→∞} (1/n) H(X₁, ..., Xₙ) = lim_{n→∞} H(Xₙ | Xₙ₋₁, ..., X₁)

MÉTHODE DE CALCUL (basée sur la Définition 60 et Théorème 10) :
MÉTHODE 1 - Via entropies jointes :
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H(X₁, ..., Xₙ) (entropie jointe, voir Formule 1)
3. Calculer (1/n) × H(X₁, ..., Xₙ)
4. Tracer la courbe de (1/n) × H(X₁, ..., Xₙ) en fonction de n
5. Estimer la limite : H(Ξ) = lim_{n→∞} (1/n) H(X₁, ..., Xₙ)

MÉTHODE 2 - Via entropies conditionnelles (équivalente pour processus stationnaires) :
1. Calculer uₙ = H(Xₙ | X₁, ..., Xₙ₋₁) pour n = 1, 2, 3, ...
2. Propriété : la suite uₙ est décroissante et converge
3. H(Ξ) = lim_{n→∞} H(Xₙ | Xₙ₋₁, ..., X₁)

4. ENTROPIE MÉTRIQUE (KOLMOGOROV-SINAI)
Formule : h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

Ce qu'elle calcule :
Entropie dynamique du système
Taux de création d'information par le système dynamique
Application baccarat : Entropie métrique normalisée Mt5 = H_AEP / log₂(18)

SOURCE EXACTE : cours_entropie/niveau_expert/01_entropie_metrique.md (lignes 5, 14, 25, 30)
- Ligne 5 : "entropie métrique de Kolmogorov-Sinai"
- Ligne 14 : "Entropie Métrique (Kolmogorov-Sinai)"
- Ligne 25 : "h_μ(T, α) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)"
- Ligne 30 : "h_μ(T) = sup{h_μ(T, α) : α partition finie}"

MÉTHODE DE CALCUL (basée sur les lignes 25-30) :
1. Choisir une partition mesurable α = {A₁, A₂, ..., Aₖ} de l'espace de phases
2. Pour chaque n, construire la partition jointe : ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα
   - T⁻ⁱα = {T⁻ⁱA₁, T⁻ⁱA₂, ..., T⁻ⁱAₖ} (partition transformée)
   - ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα = {⋂ᵢ₌₀ⁿ⁻¹ T⁻ⁱAⱼᵢ : j₀,...,jₙ₋₁ ∈ {1,...,k}}
3. Calculer H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) = -∑ᵢ μ(Bᵢ) log μ(Bᵢ) où Bᵢ sont les éléments de la partition jointe
4. Calculer (1/n) × H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
5. Prendre la limite : h_μ(T, α) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
6. Prendre le supremum sur toutes les partitions : h_μ(T) = sup{h_μ(T, α) : α partition finie}

MÉTHODE PRATIQUE D'ESTIMATION :
- Estimer via blocs de longueur croissante dans la séquence
- Calculer l'entropie par symbole pour chaque longueur de bloc
- Extrapoler la limite asymptotique par régression

5. ENTROPIE CONDITIONNELLE CUMULATIVE
Formule : H(Xₙ|X₁, X₂, ..., Xₙ₋₁) = H(X₁, ..., Xₙ) - H(X₁, ..., Xₙ₋₁)

Ce qu'elle calcule :
Information apportée par le n-ème symbole connaissant tous les précédents
Mesure la prédictibilité du système
Application baccarat : Prédictibilité de la main n connaissant l'historique

SOURCE EXACTE : cours_entropie/niveau_debutant/03_entropie_conditionnelle.md (lignes 2, 24, 58, 63)
- Ligne 2 : "Entropie Conditionnelle et Information Mutuelle"
- Ligne 24 : "L'entropie conditionnelle H(Y|X) mesure l'incertitude sur Y quand on connaît X"
- Ligne 58 : "Calcul de l'Entropie Conditionnelle"
- Ligne 63 : "H(Y|X) = Moyenne pondérée des entropies H(Y|X=x) pour chaque valeur x"

MÉTHODE DE CALCUL (basée sur les lignes 58-63) :
MÉTHODE 1 - Via différence d'entropies jointes :
1. Calculer H(X₁, ..., Xₙ) (entropie jointe de n variables, voir Formule 1)
2. Calculer H(X₁, ..., Xₙ₋₁) (entropie jointe de n-1 variables)
3. Appliquer la formule : H(Xₙ|X₁, ..., Xₙ₋₁) = H(X₁, ..., Xₙ) - H(X₁, ..., Xₙ₋₁)

MÉTHODE 2 - Via moyenne pondérée (définition théorique) :
1. Pour chaque valeur possible x₁,...,xₙ₋₁ du contexte
2. Calculer p(x₁,...,xₙ₋₁) = probabilité marginale du contexte
3. Calculer H(Xₙ|X₁=x₁,...,Xₙ₋₁=xₙ₋₁) pour ce contexte spécifique
4. Moyenne pondérée : H(Xₙ|X₁,...,Xₙ₋₁) = ∑ p(x₁,...,xₙ₋₁) × H(Xₙ|X₁=x₁,...,Xₙ₋₁=xₙ₋₁)

PROPRIÉTÉ IMPORTANTE : Pour processus stationnaires, la suite uₙ = H(Xₙ|X₁, ..., Xₙ₋₁) est décroissante
🔬 FORMULES AVANCÉES POUR FENÊTRES CROISSANTES

6. ENTROPIE RELATIVE (DIVERGENCE KL) CUMULATIVE
Formule : D_KL(P_n||Q_n) = ∑ᵢ₌₁ⁿ p(xᵢ) log₂(p(xᵢ)/q(xᵢ))

Ce qu'elle calcule :
Distance entre distribution observée et théorique sur la fenêtre croissante
Mesure l'écart par rapport au modèle théorique
Application baccarat : Écart entre fréquences observées et probabilités INDEX5

SOURCE EXACTE : cours_entropie/niveau_intermediaire/01_entropie_relative.md (lignes 2, 14, 19-21, 28)
- Ligne 2 : "Entropie Relative et Divergence de Kullback-Leibler"
- Ligne 14 : "Formule de la Divergence KL"
- Lignes 19-21 : "D(p||q) = ∑ p(x) log₂(p(x)/q(x))"
- Ligne 28 : "D(p||q) : Divergence de Kullback-Leibler"

MÉTHODE DE CALCUL (basée sur les lignes 19-33) :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p(xᵢ) = fréquence observée du symbole xᵢ dans la séquence
3. Calculer q(xᵢ) = probabilité théorique du symbole xᵢ
4. Vérifier que q(xᵢ) > 0 pour tout i (sinon D_KL = +∞)
5. Calculer le terme : p(xᵢ) × log₂(p(xᵢ)/q(xᵢ))
6. Sommer tous les termes : D_KL = ∑ᵢ₌₁ⁿ p(xᵢ) log₂(p(xᵢ)/q(xᵢ))

GESTION DES CAS LIMITES :
- Si p(xᵢ) = 0 : le terme p(xᵢ) × log₂(p(xᵢ)/q(xᵢ)) = 0 par convention
- Si q(xᵢ) = 0 mais p(xᵢ) > 0 : D_KL = +∞ (divergence infinie)
- Si p(xᵢ) = q(xᵢ) = 0 : le terme contribue 0 à la somme

PROPRIÉTÉS : D_KL(P||Q) ≥ 0 avec égalité ssi P = Q ; Non-symétrique : D_KL(P||Q) ≠ D_KL(Q||P)

7. INFORMATION MUTUELLE CUMULATIVE
Formule : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) + H(Y₁ⁿ) - H(X₁ⁿ, Y₁ⁿ)

Ce qu'elle calcule :
Dépendance entre deux séquences croissantes
Information partagée entre les séquences
Application baccarat : Corrélation entre différents patterns de jeu

SOURCE EXACTE : cours_entropie/niveau_debutant/03_entropie_conditionnelle.md (lignes 87, 91, 100)
- Ligne 87 : "L'Information Mutuelle : Mesurer la Dépendance"
- Ligne 91 : "L'information mutuelle I(X;Y) mesure combien X et Y sont 'liés' ou 'dépendants'"
- Ligne 100 : "I(X;Y) = H(Y) - H(Y|X)"

MÉTHODE DE CALCUL (basée sur les lignes 87-100) :
1. Construire la distribution de probabilités jointes p(x₁ⁿ, y₁ⁿ) pour les deux séquences
2. Calculer les distributions marginales :
   - p(x₁ⁿ) = ∑_{y₁ⁿ} p(x₁ⁿ, y₁ⁿ) (marginalisation sur Y)
   - p(y₁ⁿ) = ∑_{x₁ⁿ} p(x₁ⁿ, y₁ⁿ) (marginalisation sur X)
3. Calculer les entropies selon la Formule 1 :
   - H(X₁ⁿ) = -∑ p(x₁ⁿ) log₂ p(x₁ⁿ)
   - H(Y₁ⁿ) = -∑ p(y₁ⁿ) log₂ p(y₁ⁿ)
   - H(X₁ⁿ, Y₁ⁿ) = -∑ p(x₁ⁿ, y₁ⁿ) log₂ p(x₁ⁿ, y₁ⁿ)
4. Appliquer la formule : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) + H(Y₁ⁿ) - H(X₁ⁿ, Y₁ⁿ)

FORMULE ALTERNATIVE : I(X₁ⁿ; Y₁ⁿ) = H(X₁ⁿ) - H(X₁ⁿ|Y₁ⁿ) = H(Y₁ⁿ) - H(Y₁ⁿ|X₁ⁿ)
PROPRIÉTÉS : I(X;Y) = I(Y;X) (symétrique) ; I(X;Y) ≥ 0 ; I(X;Y) = 0 si X et Y indépendants

8. ENTROPIE CROISÉE CUMULATIVE
Formule : H_cross(P_n, Q_n) = -∑ᵢ₌₁ⁿ p(xᵢ) log₂ q(xᵢ)

Ce qu'elle calcule :
Coût d'encodage avec une distribution incorrecte
Mesure l'efficacité de prédiction
Application baccarat : Efficacité du modèle INDEX5 pour prédire

SOURCE EXACTE : cours_entropie/niveau_intermediaire/01_entropie_relative.md (lignes 127, 129, 132, 135)
- Ligne 127 : "Relation avec l'Entropie Croisée"
- Ligne 129 : "D(p||q) = H(p,q) - H(p)"
- Ligne 132 : "H(p,q) = -∑ p(x) log₂(q(x)) (entropie croisée)"
- Ligne 135 : "La divergence KL est l'excès d'entropie croisée par rapport à l'entropie vraie"

MÉTHODE DE CALCUL (basée sur les lignes 127-135) :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p(xᵢ) = fréquence observée du symbole xᵢ
3. Calculer q(xᵢ) = probabilité théorique/prédite du symbole xᵢ
4. Calculer le terme : p(xᵢ) × log₂(q(xᵢ))
5. Sommer tous les termes avec signe négatif : H_cross = -∑ᵢ₌₁ⁿ p(xᵢ) log₂ q(xᵢ)

RELATION AVEC DIVERGENCE KL : D_KL(P||Q) = H_cross(P,Q) - H(P)
- H_cross(P,Q) = -∑ p(x) log₂ q(x) (entropie croisée)
- H(P) = -∑ p(x) log₂ p(x) (entropie de Shannon)
- Donc : H_cross(P,Q) = H(P) + D_KL(P||Q)

INTERPRÉTATION : L'entropie croisée est l'excès de bits nécessaires pour encoder P avec le code optimal pour Q

9. ENTROPIE TOPOLOGIQUE CUMULATIVE
Formule : h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε)

Ce qu'elle calcule :
Complexité topologique du système dynamique
Croissance exponentielle des orbites distinctes
Application baccarat : Complexité géométrique des trajectoires de jeu

SOURCE EXACTE : cours_entropie/niveau_expert/02_entropie_topologique.md (lignes 2, 25, 42, 44)
- Ligne 2 : "Entropie Topologique et Complexité Dynamique"
- Ligne 25 : "Entropie topologique"
- Ligne 42 : "Entropie topologique"
- Ligne 44 : "h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε)"

MÉTHODE DE CALCUL (basée sur les lignes 30-44) :
DÉFINITION VIA ENSEMBLES (n,ε)-SÉPARÉS :
1. Pour chaque ε > 0 et n ≥ 1
2. Définir la distance dynamique : d_n(x,y) = max_{0≤i≤n-1} d(f^i(x), f^i(y))
3. Trouver le plus grand ensemble E ⊂ X tel que pour x,y ∈ E distincts : d_n(x,y) ≥ ε
4. Calculer s_n(ε) = |E| (cardinalité de l'ensemble maximal (n,ε)-séparé)
5. Calculer (1/n) log s_n(ε)
6. Prendre la limite : h_top(f,ε) = lim_{n→∞} (1/n) log s_n(ε)
7. Prendre la limite : h_top(f) = lim_{ε→0} h_top(f,ε)

MÉTHODE ALTERNATIVE VIA ENSEMBLES (n,ε)-SPANNING :
- r_n(ε) = min{|E| : E est (n,ε)-spanning}
- h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log r_n(ε)

MÉTHODE PRATIQUE D'ESTIMATION :
- Approximation par comptage de blocs distincts de longueur croissante
- Estimation de s_n(ε) par le nombre de motifs différents observés
📐 FORMULES SPÉCIALISÉES POUR L'ANALYSE TEMPORELLE

10. ENTROPIE DE BLOCK CUMULATIVE
Formule : H_n = H(X₁, X₂, ..., Xₙ) avec H_{n+1} ≥ H_n

Ce qu'elle calcule :
Séquence croissante d'entropies de blocs
Évolution de l'information avec la longueur
Application baccarat : Croissance de l'information avec le nombre de mains

SOURCE EXACTE : IDENTIQUE À LA FORMULE 1
- cours_entropie/resume_D4MA1C20_2012.md (lignes 912, 1160, 1723-1725)
- Propriété de croissance implicite dans la définition de l'entropie jointe

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 1 (ENTROPIE DE SHANNON JOINTE)
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H_n = H(X₁, X₂, ..., Xₙ) selon la méthode de la Formule 1
3. Vérifier la propriété de croissance : H_{n+1} ≥ H_n
4. Tracer la courbe H_n en fonction de n pour visualiser la croissance
5. Analyser la vitesse de croissance : ΔH_n = H_{n+1} - H_n

PROPRIÉTÉ FONDAMENTALE (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md) :
- H(X₁, ..., X_{n+1}) ≥ H(X₁, ..., Xₙ) toujours vraie
- L'ajout d'une variable ne peut que maintenir ou augmenter l'entropie jointe

11. ENTROPIE CONDITIONNELLE DÉCROISSANTE
Formule : u_n = H(Xₙ|X₁, ..., Xₙ₋₁) avec u_{n+1} ≤ u_n

Ce qu'elle calcule :
Séquence décroissante d'entropies conditionnelles
Amélioration de la prédictibilité avec l'historique
Application baccarat : Amélioration de la prédiction avec plus d'historique

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 923-925, 930-932)
- Théorème 10 : H(Ξ) = lim_{n→∞} H(Xₙ | Xₙ₋₁, ..., X₁)
- Lignes 930-932 : "H(X_{n+1}|X_n, ..., X_1) ≤ H(X_n|X_{n-1}, ..., X_1)" (propriété de décroissance)
- "Une suite décroissante positive ayant une limite, la suite uₙ = H(Xₙ|Xₙ₋₁, ..., X₁) converge"

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 5 (ENTROPIE CONDITIONNELLE CUMULATIVE)
1. Pour chaque position n = 2, 3, 4, ..., N
2. Calculer u_n = H(Xₙ|X₁, ..., Xₙ₋₁) selon la méthode de la Formule 5
3. Vérifier la propriété de décroissance : u_{n+1} ≤ u_n
4. Tracer la courbe u_n en fonction de n pour visualiser la décroissance
5. Analyser la vitesse de décroissance : Δu_n = u_{n+1} - u_n ≤ 0

PROPRIÉTÉ FONDAMENTALE (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Théorème 10) :
- Pour processus stationnaires : H(X_{n+1}|X_n, ..., X_1) ≤ H(X_n|X_{n-1}, ..., X_1)
- "Conditionner diminue l'entropie" : plus d'historique améliore la prédictibilité
- La suite u_n converge vers H(Ξ) (taux d'entropie du processus)

12. THÉORÈME AEP (SHANNON-MCMILLAN-BREIMAN)
Formule : lim_{n→∞} -(1/n) log₂ p(X₁ⁿ) = H(Ξ) presque sûrement

Ce qu'elle calcule :
Convergence de l'information par symbole vers l'entropie du processus
Propriété d'équipartition asymptotique
Application baccarat : Convergence vers l'entropie théorique INDEX5

SOURCE EXACTE : IDENTIQUE À LA FORMULE 2
- cours_entropie/resume_D4MA1C20_2012.md (lignes 1151-1154)
- Théorème 12 (Shannon, McMillan, Breiman)
- "lim_{n→∞} -(1/n) log₂ p₁ⁿ(X₁ⁿ) = H(Ξ) presque sûrement"

MÉTHODE DE CALCUL : IDENTIQUE À LA FORMULE 2 (ENTROPIE PAR SYMBOLE AEP)
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer p(X₁ⁿ) = probabilité de la séquence observée X₁ⁿ = [x₁, x₂, ..., xₙ]
3. Calculer -(1/n) log₂ p(X₁ⁿ) = -(1/n) log₂(∏ᵢ₌₁ⁿ p(xᵢ)) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p(xᵢ)
4. Tracer la courbe de -(1/n) log₂ p(X₁ⁿ) en fonction de n
5. Vérifier la convergence vers H(Ξ) quand n → ∞

THÉORÈME FONDAMENTAL (vérifiée dans cours_entropie/resume_D4MA1C20_2012.md, Théorème 12) :
- Pour processus stationnaires : convergence presque sûre vers H(Ξ)
- Base théorique de la compression de données et du codage de source
- Justifie l'utilisation de l'entropie comme mesure de complexité informationnelle

LIEN AVEC FORMULE 2 : Cette formule EST la Formule 2 dans sa forme limite asymptotique