"""
Script de test pour valider le nouveau analyseur d'entropie avec 24 fonctions.
"""

# Inclusion du programme principal
include("nouveau_analyseur_entropie.jl")

function test_basic_functionality()
    println("🧪 TEST DU NOUVEAU ANALYSEUR D'ENTROPIE - 24 FONCTIONS")
    println("═" ^ 60)
    
    # Test 1: Création de l'analyseur
    println("📋 Test 1: Création de l'analyseur...")
    local analyseur
    try
        analyseur = MonAnalyseur{Float64}()
        println("✅ Analyseur créé avec succès")
        println("   • Base: $(analyseur.base)")
        println("   • Epsilon: $(analyseur.epsilon)")
        println("   • Probabilités théoriques: $(length(analyseur.probabilites_theoriques)) valeurs")
    catch e
        println("❌ Erreur création analyseur: $e")
        return false
    end
    
    # Test 2: Fonctions utilitaires
    println("\n📋 Test 2: Fonctions utilitaires...")
    try
        # Test safe_log
        result = safe_log(0.5, 2.0, 1e-12)
        println("✅ safe_log(0.5, 2.0, 1e-12) = $result")
        
        # Test probabilités observées
        test_sequence = ["0_A_BANKER", "1_B_PLAYER", "0_A_BANKER", "0_C_TIE"]
        probs_obs = calculer_probabilites_observees(test_sequence)
        println("✅ Probabilités observées calculées: $(length(probs_obs)) valeurs")
        
    catch e
        println("❌ Erreur fonctions utilitaires: $e")
        return false
    end
    
    # Test 3: Chargement de données (si le fichier existe)
    println("\n📋 Test 3: Chargement de données...")
    filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"
    
    if isfile(filepath)
        try
            parties = load_baccarat_data(filepath)
            if !isempty(parties)
                println("✅ Données chargées: $(length(parties)) parties")
                
                # Test extraction séquence
                sequence = extract_index5_sequence(parties[1])
                println("✅ Séquence extraite: $(length(sequence)) éléments")
                
                if length(sequence) >= 5
                    # Test des 24 fonctions avec une petite séquence
                    test_seq = sequence[1:5]
                    println("\n📋 Test 4: Calcul des 24 fonctions...")
                    
                    try
                        # Test quelques fonctions représentatives
                        f1A = calculer_formule1A_shannon_jointe_obs(analyseur, test_seq)
                        f1B = calculer_formule1B_shannon_jointe_theo(analyseur, test_seq)
                        f2A = calculer_formule2A_aep_obs(analyseur, test_seq)
                        f2B = calculer_formule2B_aep_theo(analyseur, test_seq)
                        
                        println("✅ Formule 1A (Shannon Obs): $f1A")
                        println("✅ Formule 1B (Shannon Theo): $f1B")
                        println("✅ Formule 2A (AEP Obs): $f2A")
                        println("✅ Formule 2B (AEP Theo): $f2B")
                        
                        # Test évolution complète
                        evolution = calculer_evolution_complete_24fonctions(analyseur, test_seq)
                        println("✅ Évolution calculée: $(length(evolution)) positions")
                        
                        # Test analyse complète
                        results = analyze_single_game_24fonctions(analyseur, parties[1], "Test")
                        if !haskey(results, "error")
                            println("✅ Analyse complète réussie")
                            println("   • Longueur séquence: $(results["sequence_length"])")
                            println("   • Valeurs uniques: $(results["unique_values_count"])")
                        else
                            println("❌ Erreur analyse: $(results["error"])")
                        end
                        
                    catch e
                        println("❌ Erreur calcul fonctions: $e")
                        return false
                    end
                end
            else
                println("⚠️  Aucune partie trouvée dans le fichier")
            end
        catch e
            println("❌ Erreur chargement données: $e")
        end
    else
        println("⚠️  Fichier de données non trouvé: $filepath")
    end
    
    # Test 5: Affichage des probabilités théoriques
    println("\n📋 Test 5: Affichage des probabilités théoriques...")
    try
        display_theoretical_probabilities_24fonctions(analyseur)
        println("✅ Affichage des probabilités réussi")
    catch e
        println("❌ Erreur affichage probabilités: $e")
        return false
    end
    
    println("\n" * "═" ^ 60)
    println("🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!")
    println("✅ Le nouveau programme avec 24 fonctions est opérationnel")
    println("═" ^ 60)
    
    return true
end

# Exécution des tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_basic_functionality()
end
