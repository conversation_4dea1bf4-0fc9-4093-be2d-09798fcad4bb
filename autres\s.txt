Dans entropie_baccarat_analyzer.jl
Structurer le code pour
dédier une section réservée à toutes les métriques :
DiffC = DiffCond (Différentiel Entropie Conditionnelle) 
• DiffT = DiffTaux (Différentiel Taux d'Entropie) 
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique) 
• EntG = DiffEntropG (Différentiel Entropie Générale)
• SCORE = (DiffC + EntG) / (DiffT + DivEG)
Métrique 
Conditionnelle 
Taux 
EntropG 
DivEntropG 
DiffCond 
DiffTaux 
DiffDivEntropG 
DiffEntropG