#!/usr/bin/env julia
# ═══════════════════════════════════════════════════════════════════════════════
# INDEX5 PREDICTIVE SIMULATOR
# ═══════════════════════════════════════════════════════════════════════════════
"""
INDEX5 PREDICTIVE SIMULATOR - Simulateur de Prédiction INDEX5
=============================================================

OBJECTIF:
    Programme de simulation prédictive pour le baccarat basé sur INDEX5.
    Calcule les 7 métriques d'entropie (DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC)
    pour les 9 valeurs INDEX5 possibles à chaque main n+1.

FONCTIONNEMENT:
    - Analyse automatique du fichier le plus récent du dossier partie
    - Simulation des mains 5 à 59 (pour prédire mains 6 à 60)
    - Application des règles INDEX1 déterministes
    - Calcul exhaustif des métriques d'entropie

ARCHITECTURE:
    - Moteur de calcul: entropie_baccarat_analyzer.jl
    - Structures de données spécialisées
    - Pipeline de simulation automatisé
    - Export multi-format (TXT, CSV)

AUTEUR: Système d'analyse entropique
VERSION: 1.0
DATE: 2025-01-10
"""

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 1: IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════════════════

# Import du module principal d'analyse d'entropie
include("entropie_baccarat_analyzer.jl")

# Imports système
using Printf
using Statistics
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 2: STRUCTURES DE DONNÉES SPÉCIALISÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section définit les structures de données utilisées pour stocker
# les résultats de simulation et organiser les calculs de métriques.
#
# STRUCTURES PRINCIPALES:
# - PredictiveSimulationResult: Résultat pour une valeur INDEX5
# - HandSimulationResults: Résultats pour une main complète (6 simulations)
# - PredictionAccuracyCounter: Compteur de précision des prédictions INDEX3
# ═══════════════════════════════════════════════════════════════════════════════

"""
    PredictionAccuracyCounter

Structure pour compter la précision des prédictions INDEX3 selon les critères:
- Si INDEX3 prédit = INDEX3 observé → Prédiction correcte
- Si INDEX3 observé = TIE → Ni correcte ni incorrecte (exclue)
- Sinon → Prédiction incorrecte

Inclut également les compteurs de séries consécutives.
"""
mutable struct PredictionAccuracyCounter
    total_predictions::Int              # Nombre total de prédictions effectuées
    correct_predictions::Int            # Nombre de prédictions INDEX3 correctes
    incorrect_predictions::Int          # Nombre de prédictions INDEX3 incorrectes
    tie_excluded::Int                  # Nombre de mains avec INDEX3=TIE (exclues)

    # Compteurs de séries consécutives
    current_correct_streak::Int         # Série actuelle de prédictions correctes
    current_incorrect_streak::Int       # Série actuelle de prédictions incorrectes
    max_correct_streak::Int            # Plus longue série de prédictions correctes
    max_incorrect_streak::Int          # Plus longue série de prédictions incorrectes

    # Constructeur par défaut
    PredictionAccuracyCounter() = new(0, 0, 0, 0, 0, 0, 0, 0)
end

"""
    get_accuracy_percentage(counter::PredictionAccuracyCounter) -> Float64

Calcule le pourcentage de précision en excluant les TIE.
"""
function get_accuracy_percentage(counter::PredictionAccuracyCounter)
    evaluated_predictions = counter.correct_predictions + counter.incorrect_predictions
    if evaluated_predictions == 0
        return 0.0
    end
    return (counter.correct_predictions / evaluated_predictions) * 100.0
end

"""
    display_accuracy_summary(counter::PredictionAccuracyCounter)

Affiche un résumé détaillé de la précision des prédictions.
"""
function display_accuracy_summary(counter::PredictionAccuracyCounter)
    println("\n" * "="^80)
    println("🎯 RÉSUMÉ DE PRÉCISION DES PRÉDICTIONS INDEX3")
    println("="^80)
    println("📊 Total prédictions effectuées: $(counter.total_predictions)")
    println("✅ Prédictions INDEX3 correctes: $(counter.correct_predictions)")
    println("❌ Prédictions INDEX3 incorrectes: $(counter.incorrect_predictions)")
    println("⚪ Mains TIE exclues: $(counter.tie_excluded)")

    evaluated = counter.correct_predictions + counter.incorrect_predictions
    println("📈 Mains évaluées (hors TIE): $evaluated")

    if evaluated > 0
        accuracy = get_accuracy_percentage(counter)
        println("🎯 PRÉCISION INDEX3: $(round(accuracy, digits=2))%")

        # Comparaison avec le hasard
        random_chance = 50.0  # 50% pour BANKER vs PLAYER
        if accuracy > random_chance
            improvement = accuracy - random_chance
            println("📈 Amélioration vs hasard: +$(round(improvement, digits=2))%")
        elseif accuracy < random_chance
            degradation = random_chance - accuracy
            println("📉 Performance vs hasard: -$(round(degradation, digits=2))%")
        else
            println("⚖️  Performance égale au hasard")
        end
    else
        println("⚠️  Aucune main évaluable (toutes TIE)")
    end

    # Affichage des séries consécutives
    println("\n🔥 SÉRIES CONSÉCUTIVES:")
    println("✅ Plus longue série correcte: $(counter.max_correct_streak)")
    println("❌ Plus longue série incorrecte: $(counter.max_incorrect_streak)")
    println("🔄 Série correcte actuelle: $(counter.current_correct_streak)")
    println("🔄 Série incorrecte actuelle: $(counter.current_incorrect_streak)")

    println("="^80)
end

"""
    extract_index3(index5::String) -> String

Extrait INDEX3 (BANKER/PLAYER/TIE) d'une valeur INDEX5.
"""
function extract_index3(index5::String)
    if isempty(index5)
        return ""
    end

    parts = split(index5, '_')
    if length(parts) >= 3
        return parts[3]
    else
        return ""
    end
end

"""
    evaluate_prediction!(
        counter::PredictionAccuracyCounter,
        predicted_index5::String,
        observed_index5::String,
        hand_position::Int
    )

Évalue une prédiction et met à jour le compteur selon les critères:
- Si INDEX3 prédit = INDEX3 observé → Prédiction correcte
- Si INDEX3 observé = TIE → Ni correcte ni incorrecte (exclue)
- Sinon → Prédiction incorrecte

Met également à jour les compteurs de séries consécutives.
MODE SILENCIEUX: Aucun affichage console.
"""
function evaluate_prediction!(
    counter::PredictionAccuracyCounter,
    predicted_index5::String,
    observed_index5::String,
    hand_position::Int
)
    # Incrémenter le total des prédictions
    counter.total_predictions += 1

    # Extraire INDEX3 des valeurs prédite et observée
    predicted_index3 = extract_index3(predicted_index5)
    observed_index3 = extract_index3(observed_index5)

    # Appliquer les critères d'évaluation
    if observed_index3 == "TIE"
        # TIE observé → Exclu de l'évaluation (ne casse pas les séries)
        counter.tie_excluded += 1
        # Les séries consécutives ne sont pas affectées par les TIE

    elseif predicted_index3 == observed_index3
        # INDEX3 correct → Prédiction réussie
        counter.correct_predictions += 1

        # Mise à jour des séries consécutives
        counter.current_correct_streak += 1
        counter.current_incorrect_streak = 0  # Reset série incorrecte

        # Mise à jour du maximum si nécessaire
        if counter.current_correct_streak > counter.max_correct_streak
            counter.max_correct_streak = counter.current_correct_streak
        end

    else
        # INDEX3 incorrect → Prédiction échouée
        counter.incorrect_predictions += 1

        # Mise à jour des séries consécutives
        counter.current_incorrect_streak += 1
        counter.current_correct_streak = 0  # Reset série correcte

        # Mise à jour du maximum si nécessaire
        if counter.current_incorrect_streak > counter.max_incorrect_streak
            counter.max_incorrect_streak = counter.current_incorrect_streak
        end
    end
end

"""
    PredictiveSimulationResult{T<:AbstractFloat}

Structure pour stocker les résultats de simulation pour une valeur INDEX5.
Contient toutes les métriques de base et différentiels calculés.
"""
struct PredictiveSimulationResult{T<:AbstractFloat}
    index5_value::String                    # Valeur INDEX5 simulée
    
    # Métriques de base (8 métriques)
    mt5::T                                  # Mt5 (Entropie Métrique)
    conditionnelle::T                       # Entropie Conditionnelle
    t5::T                                   # T5 (Taux d'Entropie)
    div_entrop_g::T                         # DivEntropG (Diversité Entropique)
    entrop_g::T                             # EntropG (Entropie Générale)
    eg_obs::T                               # EGobs (Entropie Générale Observée)
    conf_eg::T                              # ConfEG (Conformité Entropique)
    struct_eg::T                            # StructEG (Structure Entropique)
    
    # Différentiels (7 différentiels)
    diff_eg::T                              # DiffEG
    diff_seg::T                             # DiffSEG
    diff_ceg::T                             # DiffCEG
    diff_t5::T                              # DiffT5
    diff_div_eg::T                          # DiffDivEG
    diff_c::T                               # DiffC
    diff_egobs::T                           # DiffEGobs (bonus)
    
    # Score composite
    score::T                                # SCORE = (DiffC + DiffEG) / (DiffT5 + DiffDivEG)
end

"""
    HandSimulationResults{T<:AbstractFloat}

Structure pour stocker tous les résultats de simulation pour une main n.
Contient les 9 simulations possibles pour la main n+1.
"""
struct HandSimulationResults{T<:AbstractFloat}
    hand_position::Int                      # Position de la main n
    current_index5::String                  # INDEX5 observé à la main n
    required_index1::Int                    # INDEX1 obligatoire pour main n+1
    
    # Résultats pour les 9 valeurs possibles
    simulations::Dict{String, PredictiveSimulationResult{T}}
    
    # Statistiques de simulation
    best_index5::String                     # Meilleure prédiction (score max)
    best_score::T                           # Score maximal
    worst_index5::String                    # Pire prédiction (score min)
    worst_score::T                          # Score minimal
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 3: RÈGLES INDEX1 DÉTERMINISTES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section implémente les règles déterministes pour INDEX1 selon INDEX2:
#
# RÈGLES FONDAMENTALES:
# - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
# - Si INDEX2 = A : INDEX1 se conserve (0→0, 1→1)
# - Si INDEX2 = B : INDEX1 se conserve (0→0, 1→1)
#
# FONCTIONS PRINCIPALES:
# - apply_index1_rules(): Application des règles
# - generate_possible_index5_values(): Génération des 6 valeurs possibles (EXCLUT TIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    apply_index1_rules(current_index5::String) -> Int

Applique les règles déterministes INDEX1 pour déterminer INDEX1 à la main n+1.

RÈGLES EXACTES IMPLÉMENTÉES :
- Si INDEX1 = 0 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 1
- Si INDEX1 = 1 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 0 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 1 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 1
- Si INDEX1 = 0 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 0
- Si INDEX1 = 1 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 1

RÉSUMÉ :
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_rules(current_index5::String)
    if isempty(current_index5)
        return nothing
    end

    try
        parts = split(current_index5, '_')
        if length(parts) < 3
            return nothing
        end

        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]

        # APPLICATION EXACTE DES RÈGLES
        if current_index2 == "C"
            # RÈGLE C : INDEX1 s'inverse
            if current_index1 == 0
                return 1  # 0_C_* → INDEX1 = 1 pour main n+1
            else  # current_index1 == 1
                return 0  # 1_C_* → INDEX1 = 0 pour main n+1
            end
        elseif current_index2 == "A"
            # RÈGLE A : INDEX1 se conserve
            if current_index1 == 0
                return 0  # 0_A_* → INDEX1 = 0 pour main n+1
            else  # current_index1 == 1
                return 1  # 1_A_* → INDEX1 = 1 pour main n+1
            end
        elseif current_index2 == "B"
            # RÈGLE B : INDEX1 se conserve
            if current_index1 == 0
                return 0  # 0_B_* → INDEX1 = 0 pour main n+1
            else  # current_index1 == 1
                return 1  # 1_B_* → INDEX1 = 1 pour main n+1
            end
        else
            return nothing  # INDEX2 invalide
        end
    catch
        return nothing
    end
end

"""
    generate_possible_index5_values(required_index1::Int) -> Vector{String}

Génère les 6 valeurs INDEX5 possibles pour INDEX1 donné (EXCLUT TIE).
Format : INDEX1_INDEX2_INDEX3 avec INDEX2 ∈ {A,B,C} et INDEX3 ∈ {BANKER,PLAYER}
"""
function generate_possible_index5_values(required_index1::Int)
    possible_values = String[]

    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER"]  # TIE EXCLU
            push!(possible_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end

    return possible_values
end

"""
    organize_simulations_by_index1(
        simulations::Dict{String, PredictiveSimulationResult{T}},
        required_index1::Int
    ) where T -> Vector{Pair{String, PredictiveSimulationResult{T}}}

Organise les simulations dans l'ordre logique demandé (EXCLUT TIE) :
- Si INDEX1 = 0 : 0_A_BANKER, 0_B_BANKER, 0_C_BANKER, 0_A_PLAYER, 0_B_PLAYER, 0_C_PLAYER
- Si INDEX1 = 1 : 1_A_BANKER, 1_B_BANKER, 1_C_BANKER, 1_A_PLAYER, 1_B_PLAYER, 1_C_PLAYER
"""
function organize_simulations_by_index1(
    simulations::Dict{String, PredictiveSimulationResult{T}},
    required_index1::Int
) where T<:AbstractFloat

    # Ordre logique pour l'affichage (TIE EXCLU)
    if required_index1 == 0
        ordered_keys = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER"
        ]
    else  # required_index1 == 1
        ordered_keys = [
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER"
        ]
    end

    # Créer la liste ordonnée des paires (index5, simulation)
    ordered_simulations = Pair{String, PredictiveSimulationResult{T}}[]

    for key in ordered_keys
        if haskey(simulations, key)
            push!(ordered_simulations, key => simulations[key])
        end
    end

    return ordered_simulations
end

"""
    test_index1_rules()

Fonction de test pour vérifier que les règles INDEX1 sont correctement implémentées.
Teste tous les cas possibles et affiche les résultats.
"""
function test_index1_rules()
    println("\n🧪 TEST DES RÈGLES INDEX1 DÉTERMINISTES")
    println("="^60)

    # Test cases avec les règles exactes
    test_cases = [
        # RÈGLES INDEX2 = C (inversion)
        ("0_C_BANKER", 1, "0_C_BANKER → INDEX1 = 1 (inversion)"),
        ("0_C_PLAYER", 1, "0_C_PLAYER → INDEX1 = 1 (inversion)"),
        ("0_C_TIE", 1, "0_C_TIE → INDEX1 = 1 (inversion)"),
        ("1_C_BANKER", 0, "1_C_BANKER → INDEX1 = 0 (inversion)"),
        ("1_C_PLAYER", 0, "1_C_PLAYER → INDEX1 = 0 (inversion)"),
        ("1_C_TIE", 0, "1_C_TIE → INDEX1 = 0 (inversion)"),

        # RÈGLES INDEX2 = A (conservation)
        ("0_A_BANKER", 0, "0_A_BANKER → INDEX1 = 0 (conservation)"),
        ("0_A_PLAYER", 0, "0_A_PLAYER → INDEX1 = 0 (conservation)"),
        ("0_A_TIE", 0, "0_A_TIE → INDEX1 = 0 (conservation)"),
        ("1_A_BANKER", 1, "1_A_BANKER → INDEX1 = 1 (conservation)"),
        ("1_A_PLAYER", 1, "1_A_PLAYER → INDEX1 = 1 (conservation)"),
        ("1_A_TIE", 1, "1_A_TIE → INDEX1 = 1 (conservation)"),

        # RÈGLES INDEX2 = B (conservation)
        ("0_B_BANKER", 0, "0_B_BANKER → INDEX1 = 0 (conservation)"),
        ("0_B_PLAYER", 0, "0_B_PLAYER → INDEX1 = 0 (conservation)"),
        ("0_B_TIE", 0, "0_B_TIE → INDEX1 = 0 (conservation)"),
        ("1_B_BANKER", 1, "1_B_BANKER → INDEX1 = 1 (conservation)"),
        ("1_B_PLAYER", 1, "1_B_PLAYER → INDEX1 = 1 (conservation)"),
        ("1_B_TIE", 1, "1_B_TIE → INDEX1 = 1 (conservation)")
    ]

    all_passed = true

    for (input_index5, expected_index1, description) in test_cases
        result = apply_index1_rules(input_index5)

        if result == expected_index1
            println("✅ $description")
        else
            println("❌ $description - ERREUR: obtenu $result, attendu $expected_index1")
            all_passed = false
        end
    end

    println("\n" * "="^60)
    if all_passed
        println("🎉 TOUS LES TESTS PASSÉS - Règles INDEX1 correctement implémentées!")
    else
        println("⚠️  CERTAINS TESTS ONT ÉCHOUÉ - Vérifier l'implémentation")
    end

    # Test de génération des 6 valeurs possibles (EXCLUT TIE)
    println("\n🔢 TEST GÉNÉRATION DES 6 VALEURS POSSIBLES (EXCLUT TIE)")
    println("-"^60)

    for index1 in [0, 1]
        possible_values = generate_possible_index5_values(index1)
        println("INDEX1 = $index1 → $(length(possible_values)) valeurs possibles:")
        for (i, value) in enumerate(possible_values)
            println("  $i. $value")
        end
        println()
    end

    return all_passed
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 5: MOTEUR DE SIMULATION PRÉDICTIVE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient le cœur algorithmique du simulateur.
#
# FONCTIONNALITÉS PRINCIPALES:
# - Simulation exhaustive des 6 valeurs INDEX5 possibles (EXCLUT TIE)
# - Calcul des métriques d'entropie pour chaque simulation
# - Calcul des différentiels entre main n et main n+1
# - Détermination de la meilleure prédiction par score composite
#
# ALGORITHME:
# 1. Déterminer INDEX1 obligatoire selon les règles
# 2. Générer les 9 valeurs INDEX5 possibles
# 3. Pour chaque valeur: simuler et calculer les métriques
# 4. Calculer les différentiels et le score composite
# 5. Identifier la meilleure et la pire prédiction
#
# FONCTION PRINCIPALE:
# - simulate_index5_prediction(): Simulation pour une main
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_index5_prediction(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        hand_position::Int
    ) where T -> HandSimulationResults{T}

OBJECTIF:
    À la main n (hand_position), simule les 9 valeurs INDEX5 possibles pour la main n+1.
    Calcule toutes les métriques d'entropie pour chacune des 9 simulations.

PROCESSUS:
    1. Analyser INDEX5 à la main n
    2. Déterminer INDEX1 obligatoire pour main n+1 (règles déterministes)
    3. Générer les 9 valeurs INDEX5 possibles (3 INDEX2 × 3 INDEX3)
    4. Pour chaque valeur simulée:
       - Créer séquence simulée [mains 1 à n] + [main n+1 simulée]
       - Calculer toutes les métriques d'entropie
       - Calculer les différentiels par rapport à la main n
    5. Identifier la meilleure prédiction (score composite maximal)

MÉTRIQUES CALCULÉES:
    - 8 métriques de base: Mt5, Conditionnelle, T5, DivEntropG, EntropG, EGobs, ConfEG, StructEG
    - 7 différentiels: DiffEG, DiffSEG, DiffCEG, DiffT5, DiffDivEG, DiffC, DiffEGobs
    - 1 score composite pour classement
"""
function simulate_index5_prediction(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    hand_position::Int
) where T<:AbstractFloat

    if hand_position < 1 || hand_position > length(sequence)
        error("Position de main invalide: $hand_position")
    end

    # ÉTAPE 1: Analyser la main n actuelle
    current_index5 = sequence[hand_position]
    println("   🎯 Main $hand_position: INDEX5 = $current_index5")

    # ÉTAPE 2: Déterminer INDEX1 obligatoire pour main n+1 selon les règles
    required_index1 = apply_index1_rules(current_index5)
    if isnothing(required_index1)
        error("Impossible de déterminer INDEX1 pour: $current_index5")
    end
    println("   📋 INDEX1 obligatoire pour main $(hand_position + 1): $required_index1")

    # ÉTAPE 3: Générer les 6 valeurs INDEX5 possibles (EXCLUT TIE)
    possible_values = generate_possible_index5_values(required_index1)
    println("   🔢 6 valeurs INDEX5 possibles générées (TIE exclu)")

    # ÉTAPE 4: Calculer les métriques de référence (main n)
    current_sequence = sequence[1:hand_position]
    current_evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)

    if isempty(current_evolution)
        error("Impossible de calculer l'évolution pour la séquence actuelle")
    end

    current_metrics = current_evolution[end]
    println("   ✅ Métriques de référence calculées pour main $hand_position")

    # ÉTAPE 5: Dictionnaire pour stocker les résultats des 6 simulations
    simulations = Dict{String, PredictiveSimulationResult{T}}()

    # ÉTAPE 6: Simuler chaque valeur INDEX5 possible (6 simulations)
    println("   🔄 Simulation des 6 valeurs INDEX5 possibles...")
    for (i, possible_index5) in enumerate(possible_values)
        # Créer la séquence simulée: [mains 1 à n] + [main n+1 simulée]
        simulated_sequence = vcat(current_sequence, [possible_index5])

        # Calculer toutes les métriques pour la séquence simulée
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)

        if isempty(simulated_evolution)
            @warn "Échec simulation pour $possible_index5"
            continue
        end

        # Métriques de la main n+1 simulée
        simulated_metrics = simulated_evolution[end]
        
        # ÉTAPE 7: Calculer les différentiels entre main n et main n+1 simulée
        # Ces différentiels mesurent l'impact de chaque valeur INDEX5 simulée
        diff_eg = calculate_diff_entrop_g(simulated_metrics.simple_entropy_theoretical, current_metrics.simple_entropy_theoretical)
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)
        diff_t5 = calculate_diff_taux(simulated_metrics.entropy_rate, current_metrics.entropy_rate)
        diff_div_eg = calculate_diff_div_entrop_g(simulated_metrics.simple_entropy, current_metrics.simple_entropy)
        diff_c = calculate_diff_cond(simulated_metrics.conditional_entropy, current_metrics.conditional_entropy)
        diff_egobs = calculate_diff_egobs(simulated_metrics.entropy_aep_observed, current_metrics.entropy_aep_observed)

        # ÉTAPE 8: Calculer le score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            simulated_metrics.metric_entropy,           # Mt5
            diff_eg,                                    # DiffEG
            diff_div_eg,                                # DiffDivEG
            diff_c,                                     # DiffC
            diff_seg,                                   # DiffSEG
            diff_ceg,                                   # DiffCEG
            diff_t5                                     # DiffT5
        )

        # ÉTAPE 9: Créer le résultat de simulation complet
        simulation_result = PredictiveSimulationResult{T}(
            possible_index5,                            # Valeur INDEX5 simulée
            simulated_metrics.metric_entropy,           # Mt5 (Entropie Métrique)
            simulated_metrics.conditional_entropy,      # Conditionnelle
            simulated_metrics.entropy_rate,             # T5 (Taux d'Entropie)
            simulated_metrics.simple_entropy,           # DivEntropG (Diversité Entropique)
            simulated_metrics.simple_entropy_theoretical, # EntropG (Entropie Générale)
            simulated_metrics.entropy_aep_observed,     # EGobs (Entropie Générale Observée)
            simulated_metrics.conf_eg,                  # ConfEG (Conformité Entropique)
            simulated_metrics.struct_eg,                # StructEG (Structure Entropique)
            diff_eg,                                    # DiffEG
            diff_seg,                                   # DiffSEG
            diff_ceg,                                   # DiffCEG
            diff_t5,                                    # DiffT5
            diff_div_eg,                                # DiffDivEG
            diff_c,                                     # DiffC
            diff_egobs,                                 # DiffEGobs (bonus)
            score                                       # Score composite
        )

        # Stocker le résultat de cette simulation
        simulations[possible_index5] = simulation_result
    end

    # Affichage organisé des résultats par INDEX1
    println("   📊 Résultats des 6 simulations (organisés par INDEX2 et INDEX3, TIE exclu):")
    ordered_simulations = organize_simulations_by_index1(simulations, required_index1)

    for (j, (index5, sim)) in enumerate(ordered_simulations)
        score_str = @sprintf("%.4f", sim.score)
        println("     $(j). $index5 → Score: $score_str")
    end

    println("   ✅ 6 simulations terminées pour main $hand_position")
    
    # ÉTAPE 10: Analyser les résultats et identifier la meilleure prédiction
    best_index5 = ""
    best_score = T(-Inf)
    worst_index5 = ""
    worst_score = T(Inf)

    for (index5, result) in simulations
        if result.score > best_score
            best_score = result.score
            best_index5 = index5
        end
        if result.score < worst_score
            worst_score = result.score
            worst_index5 = index5
        end
    end

    println("   🏆 Meilleure prédiction: $best_index5 (Score: $(round(best_score, digits=6)))")
    println("   📉 Pire prédiction: $worst_index5 (Score: $(round(worst_score, digits=6)))")

    # ÉTAPE 11: Retourner les résultats complets de la simulation
    return HandSimulationResults{T}(
        hand_position,                  # Position de la main n
        current_index5,                 # INDEX5 observé à la main n
        required_index1,                # INDEX1 obligatoire pour main n+1
        simulations,                    # Dict des 9 simulations
        best_index5,                    # Meilleure prédiction
        best_score,                     # Score maximal
        worst_index5,                   # Pire prédiction
        worst_score                     # Score minimal
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 4: GESTION DES FICHIERS ET CHARGEMENT AUTOMATIQUE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section gère le chargement automatique des données depuis le dossier partie.
#
# FONCTIONNALITÉS:
# - Détection automatique du fichier le plus récent
# - Validation de l'existence du dossier partie
# - Tri par date de modification
# - Gestion d'erreurs robuste
#
# FONCTION PRINCIPALE:
# - find_latest_file_in_partie(): Trouve le fichier le plus récent
# ═══════════════════════════════════════════════════════════════════════════════

"""
    find_latest_file_in_partie() -> String

Trouve automatiquement le fichier le plus récent dans le dossier partie.
"""
function find_latest_file_in_partie()
    partie_dir = "partie"

    if !isdir(partie_dir)
        error("Dossier 'partie' introuvable!")
    end

    files = readdir(partie_dir)
    json_files = filter(f -> endswith(f, ".json"), files)

    if isempty(json_files)
        error("Aucun fichier JSON trouvé dans le dossier partie!")
    end

    # Trier par date de modification (plus récent en premier)
    full_paths = [joinpath(partie_dir, f) for f in json_files]
    sorted_files = sort(full_paths, by = f -> stat(f).mtime, rev = true)

    latest_file = sorted_files[1]
    println("📁 Fichier le plus récent détecté: $(basename(latest_file))")

    return latest_file
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 6: SIMULATION SPÉCIALISÉE MAINS 5-59
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section implémente la simulation spécialisée pour les mains 5 à 59.
#
# SPÉCIFICATIONS:
# - Simulation des mains 5 à 59 incluses (55 mains au total)
# - Prédiction pour les mains 6 à 60
# - Validation de la longueur minimale de séquence (60 mains)
# - Affichage de progression en temps réel
# - Gestion d'erreurs par main individuelle
#
# MÉTRIQUES CALCULÉES:
# - DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC
#
# FONCTION PRINCIPALE:
# - simulate_hands_5_to_59(): Simulation spécialisée
# ═══════════════════════════════════════════════════════════════════════════════

"""
    simulate_hands_5_to_59(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String}
    ) where T -> Vector{HandSimulationResults{T}}

Simule les 9 valeurs INDEX5 possibles pour les mains 5 à 59 (prédiction mains 6 à 60).
Calcule les 7 métriques demandées : DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC.
"""
function simulate_hands_5_to_59(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if length(sequence) < 60
        error("Séquence trop courte! Minimum 60 mains requises, trouvé: $(length(sequence))")
    end

    results = HandSimulationResults{T}[]

    println("🔄 SIMULATION SPÉCIALISÉE MAINS 5 À 59")
    println("="^60)
    println("📋 Objectif: Simuler les 6 valeurs INDEX5 possibles pour chaque main n (TIE exclu)")
    println("📊 Métriques: DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC")
    println("🎯 Prédiction: Mains 6 à 60 (55 prédictions au total)")
    println()

    # BOUCLE PRINCIPALE: Simuler de la main 5 à la main 59 (incluses)
    for hand_position in 5:59
        println("\n" * "─"^60)
        println("🎲 SIMULATION MAIN $hand_position → PRÉDICTION MAIN $(hand_position + 1)")
        println("─"^60)

        try
            # Lancer la simulation pour cette main
            simulation_result = simulate_index5_prediction(analyzer, sequence, hand_position)
            push!(results, simulation_result)

            # Affichage de progression globale
            progress = hand_position - 4
            total = 59 - 5 + 1
            percentage = round(progress / total * 100, digits=1)
            println("   📈 Progression globale: $progress/$total ($percentage%)")

        catch e
            @warn "❌ ERREUR simulation main $hand_position: $e"
            continue
        end
    end

    println("✅ Simulation terminée! $(length(results)) mains simulées avec succès")
    return results
end

"""
    simulate_hands_range(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        start_hand::Int,
        end_hand::Int
    ) where T -> Vector{HandSimulationResults{T}}

Simule les 9 valeurs INDEX5 possibles pour une plage de mains spécifiée.
Plus flexible que simulate_hands_5_to_59() pour s'adapter à différentes longueurs de parties.
"""
function simulate_hands_range(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    start_hand::Int,
    end_hand::Int
) where T<:AbstractFloat

    if start_hand < 1 || end_hand >= length(sequence) || start_hand > end_hand
        error("Plage de mains invalide: $start_hand à $end_hand (séquence: $(length(sequence)) mains)")
    end

    results = HandSimulationResults{T}[]

    println("🔄 SIMULATION PLAGE MAINS $start_hand À $end_hand")
    println("="^60)
    println("📋 Objectif: Simuler les 6 valeurs INDEX5 possibles pour chaque main n (TIE exclu)")
    println("📊 Métriques: DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC")
    println("🎯 Prédiction: Mains $(start_hand + 1) à $(end_hand + 1) ($(end_hand - start_hand + 1) prédictions au total)")
    println()

    # BOUCLE PRINCIPALE: Simuler de start_hand à end_hand (incluses)
    for hand_position in start_hand:end_hand
        println("\n" * "─"^60)
        println("🎲 SIMULATION MAIN $hand_position → PRÉDICTION MAIN $(hand_position + 1)")
        println("─"^60)

        try
            # Lancer la simulation pour cette main
            simulation_result = simulate_index5_prediction(analyzer, sequence, hand_position)
            push!(results, simulation_result)

            # Affichage de progression globale
            progress = hand_position - start_hand + 1
            total = end_hand - start_hand + 1
            percentage = round(progress / total * 100, digits=1)
            println("   📈 Progression globale: $progress/$total ($percentage%)")

        catch e
            @warn "❌ ERREUR simulation main $hand_position: $e"
            continue
        end
    end

    println("\n" * "="^60)
    println("✅ SIMULATION TERMINÉE!")
    println("📊 $(length(results)) mains simulées avec succès sur $(end_hand - start_hand + 1) demandées")
    println("="^60)

    return results
end

"""
    simulate_hands_range_silent(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        start_hand::Int,
        end_hand::Int
    ) where T -> Tuple{Vector{HandSimulationResults{T}}, PredictionAccuracyCounter}

Version silencieuse de simulate_hands_range() pour génération automatique de rapports.
Aucun affichage console, seulement progression minimale.
Retourne les résultats ET le compteur de précision.
"""
function simulate_hands_range_silent(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    start_hand::Int,
    end_hand::Int
) where T<:AbstractFloat

    if start_hand < 1 || end_hand >= length(sequence) || start_hand > end_hand
        error("Plage de mains invalide: $start_hand à $end_hand (séquence: $(length(sequence)) mains)")
    end

    results = HandSimulationResults{T}[]
    accuracy_counter = PredictionAccuracyCounter()
    total_hands = end_hand - start_hand + 1

    # MODE SILENCIEUX COMPLET: Aucun affichage console

    # BOUCLE PRINCIPALE: Simuler de start_hand à end_hand (incluses) - MODE SILENCIEUX
    for hand_position in start_hand:end_hand
        try
            # Lancer la simulation pour cette main (mode silencieux)
            simulation_result = simulate_index5_prediction_silent(analyzer, sequence, hand_position)
            push!(results, simulation_result)

            # Évaluer la prédiction si la main suivante existe
            if (hand_position + 1) <= length(sequence)
                observed_index5 = sequence[hand_position + 1]
                evaluate_prediction!(
                    accuracy_counter,
                    simulation_result.best_index5,
                    observed_index5,
                    hand_position + 1
                )
            end

        catch e
            # Mode silencieux: pas d'affichage d'erreur
            continue
        end
    end

    return results, accuracy_counter
end

"""
    simulate_index5_prediction_silent(
        analyzer::EntropyAnalyzer{T},
        sequence::Vector{String},
        hand_position::Int
    ) where T -> HandSimulationResults{T}

Version silencieuse de simulate_index5_prediction() sans affichage console.
"""
function simulate_index5_prediction_silent(
    analyzer::EntropyAnalyzer{T},
    sequence::Vector{String},
    hand_position::Int
) where T<:AbstractFloat

    if hand_position < 1 || hand_position > length(sequence)
        error("Position de main invalide: $hand_position")
    end

    # INDEX5 actuel à la main n
    current_index5 = sequence[hand_position]

    # Déterminer INDEX1 obligatoire pour main n+1
    required_index1 = apply_index1_rules(current_index5)
    if isnothing(required_index1)
        error("Impossible de déterminer INDEX1 pour: $current_index5")
    end

    # Générer les 9 valeurs possibles
    possible_values = generate_possible_index5_values(required_index1)

    # Calculer les métriques de référence (main n)
    current_sequence = sequence[1:hand_position]
    current_evolution = calculate_all_metrics_evolution(analyzer, current_sequence, 4)

    if isempty(current_evolution)
        error("Impossible de calculer l'évolution pour la séquence actuelle")
    end

    current_metrics = current_evolution[end]

    # Dictionnaire pour stocker les résultats des 6 simulations
    simulations = Dict{String, PredictiveSimulationResult{T}}()

    # Simuler chaque valeur INDEX5 possible (6 simulations) - MODE SILENCIEUX
    for possible_index5 in possible_values
        # Créer la séquence simulée: [mains 1 à n] + [main n+1 simulée]
        simulated_sequence = vcat(current_sequence, [possible_index5])

        # Calculer toutes les métriques pour la séquence simulée
        simulated_evolution = calculate_all_metrics_evolution(analyzer, simulated_sequence, 4)

        if isempty(simulated_evolution)
            continue
        end

        # Métriques de la main n+1 simulée
        simulated_metrics = simulated_evolution[end]

        # Calculer les différentiels entre main n et main n+1 simulée
        diff_eg = calculate_diff_entrop_g(simulated_metrics.simple_entropy_theoretical, current_metrics.simple_entropy_theoretical)
        diff_seg = calculate_diff_seg(simulated_metrics.struct_eg, current_metrics.struct_eg)
        diff_ceg = calculate_diff_ceg(simulated_metrics.conf_eg, current_metrics.conf_eg)
        diff_t5 = calculate_diff_taux(simulated_metrics.entropy_rate, current_metrics.entropy_rate)
        diff_div_eg = calculate_diff_div_entrop_g(simulated_metrics.simple_entropy, current_metrics.simple_entropy)
        diff_c = calculate_diff_cond(simulated_metrics.conditional_entropy, current_metrics.conditional_entropy)
        diff_egobs = calculate_diff_egobs(simulated_metrics.entropy_aep_observed, current_metrics.entropy_aep_observed)

        # Calculer le score composite selon la formule entropique optimale
        # SCORE = Mt5 × exp(-DiffEG) × exp(-DiffDivEG) × exp(-DiffC) / ((1 + DiffSEG + DiffCEG) × (1 + DiffT5))
        score = calculate_predictive_score(
            simulated_metrics.metric_entropy,           # Mt5
            diff_eg,                                    # DiffEG
            diff_div_eg,                                # DiffDivEG
            diff_c,                                     # DiffC
            diff_seg,                                   # DiffSEG
            diff_ceg,                                   # DiffCEG
            diff_t5                                     # DiffT5
        )

        # Créer le résultat de simulation complet
        simulation_result = PredictiveSimulationResult{T}(
            possible_index5,                            # Valeur INDEX5 simulée
            simulated_metrics.metric_entropy,           # Mt5 (Entropie Métrique)
            simulated_metrics.conditional_entropy,      # Conditionnelle
            simulated_metrics.entropy_rate,             # T5 (Taux d'Entropie)
            simulated_metrics.simple_entropy,           # DivEntropG (Diversité Entropique)
            simulated_metrics.simple_entropy_theoretical, # EntropG (Entropie Générale)
            simulated_metrics.entropy_aep_observed,     # EGobs (Entropie Générale Observée)
            simulated_metrics.conf_eg,                  # ConfEG (Conformité Entropique)
            simulated_metrics.struct_eg,                # StructEG (Structure Entropique)
            diff_eg,                                    # DiffEG
            diff_seg,                                   # DiffSEG
            diff_ceg,                                   # DiffCEG
            diff_t5,                                    # DiffT5
            diff_div_eg,                                # DiffDivEG
            diff_c,                                     # DiffC
            diff_egobs,                                 # DiffEGobs (bonus)
            score                                       # Score composite
        )

        # Stocker le résultat de cette simulation
        simulations[possible_index5] = simulation_result
    end

    # Analyser les résultats et identifier la meilleure prédiction
    best_index5 = ""
    best_score = T(-Inf)
    worst_index5 = ""
    worst_score = T(Inf)

    for (index5, result) in simulations
        if result.score > best_score
            best_score = result.score
            best_index5 = index5
        end
        if result.score < worst_score
            worst_score = result.score
            worst_index5 = index5
        end
    end

    # Retourner les résultats complets de la simulation
    return HandSimulationResults{T}(
        hand_position,                  # Position de la main n
        current_index5,                 # INDEX5 observé à la main n
        required_index1,                # INDEX1 obligatoire pour main n+1
        simulations,                    # Dict des 9 simulations
        best_index5,                    # Meilleure prédiction
        best_score,                     # Score maximal
        worst_index5,                   # Pire prédiction
        worst_score                     # Score minimal
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 7: AFFICHAGE ET GÉNÉRATION DE RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section gère l'affichage des résultats et la génération de rapports.
#
# FONCTIONNALITÉS D'AFFICHAGE:
# - Résumé statistique des simulations
# - Distribution des meilleures prédictions
# - Métriques globales (moyenne, médiane, min/max)
#
# FORMATS D'EXPORT:
# - Rapport détaillé TXT: Tableau complet par main
# - Export CSV: Données structurées pour analyse externe
# - Métadonnées: Date, nombre de simulations, statistiques
#
# FONCTIONS PRINCIPALES:
# - display_simulation_summary(): Affichage résumé
# - generate_detailed_simulation_report(): Rapport TXT détaillé
# - export_simulation_csv(): Export CSV structuré
# ═══════════════════════════════════════════════════════════════════════════════

"""
    display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T

Affiche un résumé des résultats de simulation.
"""
function display_simulation_summary(results::Vector{HandSimulationResults{T}}) where T<:AbstractFloat
    if isempty(results)
        println("❌ Aucun résultat de simulation à afficher")
        return
    end

    println("\n" * "="^80)
    println("📊 RÉSUMÉ DE SIMULATION PRÉDICTIVE INDEX5")
    println("="^80)
    println("📏 Nombre de mains simulées: $(length(results))")

    # Statistiques globales
    all_scores = Float64[]
    best_predictions = String[]

    for result in results
        for (index5, sim) in result.simulations
            push!(all_scores, sim.score)
        end
        push!(best_predictions, result.best_index5)
    end

    if !isempty(all_scores)
        println("📈 Score moyen: $(round(mean(all_scores), digits=6))")
        println("📊 Score médian: $(round(median(all_scores), digits=6))")
        println("📉 Score min/max: $(round(minimum(all_scores), digits=6)) / $(round(maximum(all_scores), digits=6))")
    end

    # Distribution des meilleures prédictions
    prediction_counts = Dict{String, Int}()
    for pred in best_predictions
        prediction_counts[pred] = get(prediction_counts, pred, 0) + 1
    end

    println("\n🏆 DISTRIBUTION DES MEILLEURES PRÉDICTIONS:")
    sorted_predictions = sort(collect(prediction_counts), by = x -> x[2], rev = true)
    for (index5, count) in sorted_predictions[1:min(5, length(sorted_predictions))]
        percentage = round(count / length(best_predictions) * 100, digits=2)
        println("   $index5: $count fois ($percentage%)")
    end

    println("="^80)
end

"""
    generate_detailed_simulation_report(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_report.txt",
        sequence::Vector{String} = String[],
        accuracy_counter::Union{PredictionAccuracyCounter, Nothing} = nothing
    ) where T

Génère un rapport détaillé de simulation au format texte.
Inclut l'INDEX5 observé à la main n+1 si la séquence est fournie.
Inclut le résumé de précision si le compteur est fourni.
"""
function generate_detailed_simulation_report(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_report.txt",
    sequence::Vector{String} = String[],
    accuracy_counter::Union{PredictionAccuracyCounter, Nothing} = nothing
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête du rapport
            println(file, "RAPPORT DÉTAILLÉ DE SIMULATION PRÉDICTIVE INDEX5")
            println(file, "="^60)
            println(file, "Date: $(Dates.now())")
            println(file, "Nombre de mains simulées: $(length(results))")

            # Ajouter le résumé de précision si disponible
            if !isnothing(accuracy_counter)
                println(file, "")
                println(file, "RÉSUMÉ DE PRÉCISION INDEX3:")
                println(file, "-"^40)
                println(file, "Total prédictions: $(accuracy_counter.total_predictions)")
                println(file, "Prédictions correctes: $(accuracy_counter.correct_predictions)")
                println(file, "Prédictions incorrectes: $(accuracy_counter.incorrect_predictions)")
                println(file, "Mains TIE exclues: $(accuracy_counter.tie_excluded)")

                evaluated = accuracy_counter.correct_predictions + accuracy_counter.incorrect_predictions
                if evaluated > 0
                    accuracy = get_accuracy_percentage(accuracy_counter)
                    println(file, "Précision INDEX3: $(round(accuracy, digits=2))%")

                    # Comparaison avec le hasard
                    if accuracy > 50.0
                        improvement = accuracy - 50.0
                        println(file, "Amélioration vs hasard: +$(round(improvement, digits=2))%")
                    elseif accuracy < 50.0
                        degradation = 50.0 - accuracy
                        println(file, "Performance vs hasard: -$(round(degradation, digits=2))%")
                    else
                        println(file, "Performance égale au hasard")
                    end
                end

                # Ajouter les informations sur les séries consécutives
                println(file, "")
                println(file, "SÉRIES CONSÉCUTIVES:")
                println(file, "-"^25)
                println(file, "Plus longue série correcte: $(accuracy_counter.max_correct_streak)")
                println(file, "Plus longue série incorrecte: $(accuracy_counter.max_incorrect_streak)")
                println(file, "Série correcte finale: $(accuracy_counter.current_correct_streak)")
                println(file, "Série incorrecte finale: $(accuracy_counter.current_incorrect_streak)")
            end

            println(file, "")

            # Tableau détaillé pour chaque main
            println(file, "DÉTAIL PAR MAIN:")
            println(file, "-"^60)

            for (i, result) in enumerate(results)
                println(file, "")
                println(file, "MAIN $(result.hand_position) - INDEX5 : $(result.current_index5)")
                println(file, "INDEX1 obligatoire pour main $(result.hand_position + 1): $(result.required_index1)")
                println(file, "Meilleure prédiction: $(result.best_index5) (Score: $(round(result.best_score, digits=6)))")
                println(file, "")

                # Afficher l'INDEX5 observé à la main n+1 si disponible
                if !isempty(sequence) && (result.hand_position + 1) <= length(sequence)
                    observed_index5 = sequence[result.hand_position + 1]
                    println(file, "MAIN $(result.hand_position + 1) - INDEX5 observé: $observed_index5")

                    # Évaluation de la prédiction
                    predicted_index3 = extract_index3(result.best_index5)
                    observed_index3 = extract_index3(observed_index5)

                    if observed_index3 == "TIE"
                        println(file, "Évaluation: TIE observé → Exclu")
                    elseif predicted_index3 == observed_index3
                        println(file, "Évaluation: INDEX3 correct → ✅ RÉUSSITE")
                    else
                        println(file, "Évaluation: INDEX3 incorrect → ❌ ÉCHEC")
                    end
                    println(file, "")
                end

                # Tableau des 6 simulations organisé par INDEX1 puis par ordre logique
                println(file, @sprintf("%-15s %10s %10s %10s %10s %10s %10s %10s %12s",
                        "INDEX5", "DiffEG", "DiffSEG", "DiffCEG", "Mt5", "DiffT5", "DiffDivEG", "DiffC", "SCORE"))
                println(file, "-"^115)

                # Organiser les simulations dans l'ordre logique demandé
                ordered_simulations = organize_simulations_by_index1(result.simulations, result.required_index1)

                for (index5, sim) in ordered_simulations
                    println(file, @sprintf("%-15s %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %10.4f %12.6f",
                            index5,
                            sim.diff_eg,
                            sim.diff_seg,
                            sim.diff_ceg,
                            sim.mt5,
                            sim.diff_t5,
                            sim.diff_div_eg,
                            sim.diff_c,
                            sim.score))
                end

                if i < length(results)
                    println(file, "")
                    println(file, "-"^60)
                end
            end
        end

        println("📄 Rapport détaillé généré: $filename")

    catch e
        @error "Erreur lors de la génération du rapport: $e"
    end
end

"""
    export_simulation_csv(
        results::Vector{HandSimulationResults{T}},
        filename::String = "simulation_data.csv"
    ) where T

Exporte les résultats de simulation au format CSV pour analyse externe.
"""
function export_simulation_csv(
    results::Vector{HandSimulationResults{T}},
    filename::String = "simulation_data.csv"
) where T<:AbstractFloat

    if isempty(results)
        @warn "Aucun résultat à exporter"
        return
    end

    try
        open(filename, "w") do file
            # En-tête CSV
            println(file, "Hand_Position,Current_INDEX5,Required_INDEX1,Simulated_INDEX5,Mt5,Conditionnelle,T5,DivEntropG,EntropG,EGobs,ConfEG,StructEG,DiffEG,DiffSEG,DiffCEG,DiffT5,DiffDivEG,DiffC,DiffEGobs,Score,Is_Best")

            # Données pour chaque simulation
            for result in results
                for (index5, sim) in result.simulations
                    is_best = (index5 == result.best_index5) ? "TRUE" : "FALSE"

                    println(file, "$(result.hand_position),$(result.current_index5),$(result.required_index1),$index5,$(sim.mt5),$(sim.conditionnelle),$(sim.t5),$(sim.div_entrop_g),$(sim.entrop_g),$(sim.eg_obs),$(sim.conf_eg),$(sim.struct_eg),$(sim.diff_eg),$(sim.diff_seg),$(sim.diff_ceg),$(sim.diff_t5),$(sim.diff_div_eg),$(sim.diff_c),$(sim.diff_egobs),$(sim.score),$is_best")
                end
            end
        end

        println("📊 Données CSV exportées: $filename")

    catch e
        @error "Erreur lors de l'export CSV: $e"
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 8: PROGRAMME PRINCIPAL AUTOMATISÉ
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient le programme principal qui orchestre toute la simulation.
#
# PIPELINE D'EXÉCUTION:
# 1. Initialisation de l'analyseur d'entropie
# 2. Détection automatique du fichier le plus récent
# 3. Chargement et validation des données
# 4. Extraction de la séquence INDEX5
# 5. Validation de la longueur (minimum 60 mains)
# 6. Lancement de la simulation spécialisée (mains 5-59)
# 7. Affichage des résultats
# 8. Génération automatique des rapports (TXT + CSV)
#
# GESTION D'ERREURS:
# - Try-catch global avec messages explicites
# - Validation à chaque étape critique
# - Messages de progression en temps réel
#
# FONCTION PRINCIPALE:
# - main_simulation(): Point d'entrée automatisé
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main_simulation()

Programme principal interactif pour la simulation prédictive INDEX5.
Charge automatiquement le fichier le plus récent et propose un menu de choix.
"""
function main_simulation()
    println("🎰 SIMULATEUR PRÉDICTIF INDEX5 - BACCARAT")
    println("="^60)
    println("📋 Simulation des 6 valeurs INDEX5 possibles par main (TIE exclu)")
    println("📊 Calcul des 7 métriques: DiffEG, DiffSEG, DiffCEG, Mt5, DiffT5, DiffDivEG, DiffC")
    println("🔧 Basé sur entropie_baccarat_analyzer.jl")
    println()

    try
        # Initialisation de l'analyseur
        println("⚙️  Initialisation de l'analyseur d'entropie...")
        analyzer = EntropyAnalyzer{Float64}()

        # Chargement automatique du fichier le plus récent
        println("🔍 Recherche du fichier le plus récent...")
        latest_file = find_latest_file_in_partie()

        # Chargement des données
        println("📁 Chargement des données...")
        parties = load_baccarat_data(latest_file)

        if isempty(parties)
            error("Aucune partie trouvée dans le fichier!")
        end

        println("✅ $(length(parties)) parties chargées")

        # MENU INTERACTIF
        while true
            println("\n" * "="^60)
            println("🎯 MENU SIMULATION PRÉDICTIVE INDEX5:")
            println("="^60)
            println("1. 🎲 Simuler une partie spécifique (mains 5 à dernière main)")
            println("2. 📊 Simuler toutes les parties")
            println("3. 🔍 Afficher informations sur les parties disponibles")
            println("4. 🚪 Quitter")
            println("="^60)

            print("👉 Votre choix (1-4): ")
            choix = strip(readline())

            if choix == "1"
                simulate_specific_game(analyzer, parties)
            elseif choix == "2"
                simulate_all_games(analyzer, parties)
            elseif choix == "3"
                display_games_info(parties)
            elseif choix == "4"
                println("\n👋 Au revoir! Merci d'avoir utilisé le simulateur prédictif INDEX5!")
                break
            else
                println("❌ Choix invalide! Veuillez choisir entre 1 et 4.")
            end
        end

    catch e
        println("❌ ERREUR: $e")
        println("🔧 Vérifiez que le dossier 'partie' contient des fichiers JSON valides")
    end
end

"""
    simulate_specific_game(analyzer::EntropyAnalyzer, parties::Vector)

Interface pour simuler une partie spécifique choisie par l'utilisateur.
"""
function simulate_specific_game(analyzer::EntropyAnalyzer, parties::Vector)
    println("\n🎲 SIMULATION D'UNE PARTIE SPÉCIFIQUE")
    println("="^50)
    println("📊 Parties disponibles: 1 à $(length(parties))")

    while true
        print("👉 Numéro de la partie à simuler (1-$(length(parties)), ou 'retour'): ")
        input = strip(readline())

        if lowercase(input) == "retour"
            return
        end

        try
            partie_num = parse(Int, input)
            if partie_num < 1 || partie_num > length(parties)
                println("❌ Numéro invalide! Choisissez entre 1 et $(length(parties))")
                continue
            end

            # Extraire la séquence de la partie choisie
            game_data = parties[partie_num]
            sequence = extract_index5_sequence(game_data)

            if length(sequence) < 6
                println("❌ Partie trop courte! $(length(sequence)) mains trouvées, minimum 6 requises")
                continue
            end

            println("\n📏 Partie $partie_num: $(length(sequence)) mains")
            println("🎯 Simulation des mains 5 à $(length(sequence)-1) (prédiction mains 6 à $(length(sequence)))")
            println("🚀 Lancement automatique de la simulation...")

            # Lancer automatiquement la simulation adaptée à la longueur de la partie (mode silencieux)
            results, accuracy_counter = simulate_hands_range_silent(analyzer, sequence, 5, length(sequence)-1)

            # Affichage du résumé de précision
            display_accuracy_summary(accuracy_counter)

            # Génération automatique du rapport unique et complet
            report_filename = "rapport$(partie_num).txt"
            println("📄 Génération du rapport: $report_filename")
            generate_detailed_simulation_report(results, report_filename, sequence, accuracy_counter)

            println("✅ Simulation terminée!")
            println("📊 Rapport généré: $report_filename")
            println("📈 $(length(results)) mains simulées avec $(length(results) * 6) simulations INDEX5 au total")

            break

        catch e
            println("❌ Erreur: $e")
        end
    end
end

"""
    simulate_all_games(analyzer::EntropyAnalyzer, parties::Vector)

Simule toutes les parties disponibles.
"""
function simulate_all_games(analyzer::EntropyAnalyzer, parties::Vector)
    println("\n📊 SIMULATION DE TOUTES LES PARTIES")
    println("="^50)
    println("🎯 $(length(parties)) parties à simuler")

    print("🤔 Confirmer la simulation de toutes les parties? (o/n): ")
    if lowercase(strip(readline())) != "o"
        return
    end

    all_results = HandSimulationResults{Float64}[]
    successful_simulations = 0

    for i in 1:length(parties)
        try
            game_data = parties[i]
            sequence = extract_index5_sequence(game_data)

            if length(sequence) >= 6
                results = simulate_hands_range(analyzer, sequence, 5, length(sequence)-1)
                append!(all_results, results)
                successful_simulations += 1

                if i % 10 == 0
                    println("   ✅ Parties simulées: $i/$(length(parties))")
                end
            else
                println("   ⚠️  Partie $i ignorée (trop courte: $(length(sequence)) mains)")
            end
        catch e
            @warn "❌ Erreur partie $i: $e"
            continue
        end
    end

    println("\n✅ Simulation terminée: $successful_simulations parties simulées avec succès")

    # Afficher les résultats globaux
    display_simulation_summary(all_results)

    # Proposer l'export global
    print("\n💾 Générer un rapport global? (o/n): ")
    if lowercase(strip(readline())) == "o"
        generate_detailed_simulation_report(all_results, "simulation_globale_detailed.txt")
    end

    print("💾 Exporter en CSV global? (o/n): ")
    if lowercase(strip(readline())) == "o"
        export_simulation_csv(all_results, "simulation_globale_data.csv")
    end
end

"""
    display_games_info(parties::Vector)

Affiche des informations sur les parties disponibles.
"""
function display_games_info(parties::Vector)
    println("\n🔍 INFORMATIONS SUR LES PARTIES DISPONIBLES")
    println("="^60)
    println("📊 Nombre total de parties: $(length(parties))")

    # Analyser quelques parties pour donner des statistiques
    valid_parties = 0
    total_hands = 0
    min_hands = typemax(Int)
    max_hands = 0

    for (i, game_data) in enumerate(parties[1:min(10, length(parties))])
        try
            sequence = extract_index5_sequence(game_data)
            hands_count = length(sequence)

            if hands_count >= 6
                valid_parties += 1
                total_hands += hands_count
                min_hands = min(min_hands, hands_count)
                max_hands = max(max_hands, hands_count)
            end

            if i <= 5
                status = hands_count >= 6 ? "✅ Valide" : "❌ Trop courte"
                println("   Partie $i: $hands_count mains - $status")
            end
        catch e
            println("   Partie $i: ❌ Erreur de lecture")
        end
    end

    if length(parties) > 5
        println("   ... (et $(length(parties) - 5) autres parties)")
    end

    if valid_parties > 0
        avg_hands = round(total_hands / valid_parties, digits=1)
        println("\n📈 STATISTIQUES (échantillon de 10 parties):")
        println("   • Parties valides: $valid_parties/$(min(10, length(parties)))")
        println("   • Mains par partie: $min_hands - $max_hands (moyenne: $avg_hands)")
        println("   • Simulations possibles par partie: 5 à $(max_hands-1) mains")
    end

    println("\n💡 Pour simuler: choisissez l'option 1 ou 2 du menu principal")
end

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 9: INTERFACES INTERACTIVES SPÉCIALISÉES
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section contient les interfaces interactives pour différents types
# de simulation prédictive INDEX5.
#
# FONCTIONS PRINCIPALES:
# - simulate_specific_game(): Simulation d'une partie choisie
# - simulate_all_games(): Simulation de toutes les parties
# - display_games_info(): Informations sur les parties disponibles
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# SECTION 10: POINT D'ENTRÉE ET EXÉCUTION AUTOMATIQUE
# ═══════════════════════════════════════════════════════════════════════════════
#
# Cette section finale gère l'exécution automatique du programme.
#
# COMPORTEMENT:
# - Exécution automatique si le fichier est lancé directement
# - Pas d'exécution si le fichier est inclus dans un autre programme
# - Permet l'utilisation en tant que module ou programme autonome
#
# USAGE:
# - Lancement direct: julia index5_predictive_simulator.jl
# - Inclusion: include("index5_predictive_simulator.jl")
# ═══════════════════════════════════════════════════════════════════════════════

# Exécution automatique si fichier lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_simulation()
end

# ═══════════════════════════════════════════════════════════════════════════════
# FIN DU PROGRAMME - INDEX5 PREDICTIVE SIMULATOR
# ═══════════════════════════════════════════════════════════════════════════════
